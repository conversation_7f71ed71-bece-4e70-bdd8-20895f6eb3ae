# Parquet数据源功能使用指南

## 概述

本文档介绍了为choice==3（智能关键词匹配）新增的Parquet数据源功能。该功能支持从两个指定文件夹中读取同名parquet文件，自动合并数据后进行关键词匹配处理。

## 功能特性

### 1. 双文件夹数据合并
- 从两个配置的文件夹中读取同名parquet文件
- 基于指定的标识列（默认：`lc_company_id`, `company_name`）进行数据合并
- 支持inner join合并模式，确保数据一致性

### 2. 三种处理模式
- **单文件处理**：选择单个parquet文件进行处理
- **批量处理**：自动处理所有可用的parquet文件
- **随机测试**：随机选择一个文件进行快速测试

### 3. 完整的错误处理
- 配置验证和错误提示
- 文件存在性检查
- 数据合并验证
- 详细的日志记录

## 配置说明

### config.json配置项

```json
{
  "keyword_matching": {
    "parquet_data_source": {
      "enable": true,
      "folder_path_1": "F:/蕾奥工作/11.自研挂链/输入数据整理/result_date_part01",
      "folder_path_2": "F:/蕾奥工作/11.自研挂链/输入数据整理/result_date_part02",
      "file_extension": ".parquet",
      "merge_on_columns": ["lc_company_id", "company_name"],
      "batch_processing": {
        "enable_full_batch": true,
        "enable_random_test": true,
        "progress_report_interval": 10,
        "error_handling": {
          "continue_on_error": true,
          "max_errors": 50,
          "log_errors": true
        }
      }
    }
  }
}
```

### 配置项说明

- `enable`: 是否启用Parquet数据源功能
- `folder_path_1`: 第一个parquet文件夹路径
- `folder_path_2`: 第二个parquet文件夹路径
- `merge_on_columns`: 数据合并时使用的列名
- `batch_processing`: 批处理相关配置
  - `continue_on_error`: 遇到错误时是否继续处理其他文件
  - `max_errors`: 最大允许错误数
  - `progress_report_interval`: 进度报告间隔

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 选择功能
选择 `3. 智能关键词匹配`

### 3. 选择数据源
现在会看到6个选项：
1. 手动输入文件路径
2. 从当前目录选择Excel文件
3. 从当前目录选择CSV文件
4. **从Parquet文件夹选择单个文件** ⭐
5. **批量处理所有Parquet文件** ⭐
6. **随机选择Parquet文件测试** ⭐

### 4. 处理模式详解

#### 单文件处理（选项4）
- 显示所有可用的同名parquet文件列表
- 选择一个文件进行处理
- 适合处理特定文件或小规模测试

#### 批量处理（选项5）
- 自动处理所有可用的parquet文件
- 支持进度跟踪和错误处理
- 为每个文件生成独立的结果文件
- 适合大规模数据处理

#### 随机测试（选项6）
- 随机选择一个文件进行快速测试
- 适合验证功能正确性
- 快速获得处理结果样本

## 数据处理流程

### 1. 文件发现
程序会扫描两个配置的文件夹，找到同名的parquet文件。

### 2. 数据读取
- 从文件夹1读取parquet文件
- 从文件夹2读取同名parquet文件
- 验证合并列是否存在

### 3. 数据合并
- 基于配置的`merge_on_columns`进行inner join
- 验证合并结果的完整性
- 记录合并统计信息

### 4. 关键词匹配
- 使用合并后的数据进行关键词匹配
- 支持所有原有的匹配逻辑和筛选规则
- 生成详细的匹配结果和报告

## 输出文件

### 单文件处理
- 结果文件：`matching_results_YYYYMMDD_HHMMSS.xlsx`
- 摘要报告：`summary_report_YYYYMMDD_HHMMSS.xlsx`

### 批量处理
- 结果文件：`batch_results_文件名_YYYYMMDD_HHMMSS.xlsx`
- 摘要报告：`summary_report_文件名_YYYYMMDD_HHMMSS.xlsx`

### 随机测试
- 结果文件：`random_test_文件名_YYYYMMDD_HHMMSS.xlsx`
- 摘要报告：`summary_report_文件名_YYYYMMDD_HHMMSS.xlsx`

## 日志和监控

### 日志文件
- 位置：`keyword_matching_YYYYMMDD.log`
- 包含详细的处理过程、错误信息和性能统计

### 进度监控
- 实时显示处理进度
- 显示成功/失败统计
- 批量处理时显示文件级别进度

## 故障排除

### 常见问题

1. **没有找到可用的Parquet文件**
   - 检查配置的文件夹路径是否正确
   - 确认两个文件夹都存在且包含同名的parquet文件

2. **数据合并失败**
   - 检查合并列是否在两个文件中都存在
   - 确认列名拼写正确

3. **配置验证失败**
   - 检查config.json中的parquet_data_source配置
   - 确认文件夹路径使用正斜杠或双反斜杠

### 调试方法

1. **运行测试脚本**
   ```bash
   python test_parquet_functionality.py
   ```

2. **检查日志文件**
   查看详细的错误信息和处理过程

3. **使用随机测试模式**
   快速验证功能是否正常工作

## 性能优化

### 建议设置
- 批处理大小：1000（默认）
- 并行线程数：4（默认）
- 启用性能优化：true

### 大数据处理
- 对于大量文件的批处理，建议分批进行
- 监控内存使用情况
- 适当调整批处理大小和线程数

## 兼容性说明

- 新功能完全向后兼容，不影响原有的Excel/CSV文件处理
- 原有的choice==3功能保持不变
- 可以随时通过配置禁用Parquet功能

## 更新历史

- **v2.0** - 新增Parquet数据源功能
  - 支持双文件夹数据合并
  - 新增批量处理和随机测试模式
  - 完善的错误处理和日志记录
