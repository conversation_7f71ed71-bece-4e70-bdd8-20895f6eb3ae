#!/usr/bin/env python3
"""
挂链工具集 - 主入口文件
Link Tool Suite - Main Entry Point

这是整个项目的统一入口点，提供对各个功能模块的访问
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入子功能模块
import cli.keyword_rules_validator_cli as keyword_rules_validator_cli
import cli.keyword_converter_cli as keyword_converter_cli
import cli.keyword_matcher_cli as keyword_matcher_cli


def show_main_menu():
    """显示主菜单"""
    print("=" * 60)
    print("挂链工具集 - 主菜单")
    print("=" * 60)
    print("\n请选择要使用的功能：\n")
    print("1. 关键词规则验证")
    print("2. 关键词模式转换")
    print("3. 智能关键词匹配")
    print("4. 退出程序")
    
    while True:
        try:
            choice = input("\n请输入选项编号 [1-4]: ")
            if choice in ['1', '2', '3', '4']:
                return choice
            else:
                print("❌ 无效的选项，请重新输入")
        except KeyboardInterrupt:
            print("\n程序已中断")
            return '4'


def main():
    """主函数"""
    while True:
        choice = show_main_menu()
        
        if choice == '1':
            # 运行关键词规则验证器
            print("\n正在启动关键词规则验证器...\n")
            keyword_rules_validator_cli.main()
        elif choice == '2':
            # 运行关键词模式转换器
            print("\n正在启动关键词模式转换器...\n")
            keyword_converter_cli.main()
        elif choice == '3':
            # 运行智能关键词匹配器
            print("\n正在启动智能关键词匹配器...\n")
            keyword_matcher_cli.main()
        elif choice == '4':
            print("\n感谢使用挂链工具集，再见！")
            sys.exit(0)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序已中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序执行出错：{e}")
        sys.exit(1) 