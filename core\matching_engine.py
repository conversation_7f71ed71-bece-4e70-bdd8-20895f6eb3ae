#!/usr/bin/env python3
"""
匹配引擎模块
Matching Engine Module

该模块实现核心的关键词匹配逻辑，支持like/must/unlike三步匹配流程，
提供高效的文本匹配和标记功能。

匹配流程：
1. Like关键词匹配：检查是否存在，记录匹配信息
2. Must关键词匹配：检查是否存在，记录匹配信息
3. Unlike关键词匹配：检查是否存在，如存在则匹配失败

作者：系统开发
日期：2024年
"""

import logging
import json
import re
from typing import List, Dict, Set, Optional, Any, Tuple, Union
from dataclasses import dataclass
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import config_manager
from core.keyword_compiler import KeywordCompiler, CompiledPattern


@dataclass
class MatchResult:
    """匹配结果数据类"""
    success: bool
    matched_texts: Dict[str, List[str]]  # 列名 -> 匹配文本列表
    failure_reason: Optional[str] = None
    match_details: Optional[Dict[str, Any]] = None


class MatchingEngine:
    """
    匹配引擎类
    
    负责执行核心的关键词匹配逻辑，
    支持三步匹配流程和多种匹配模式。
    """
    
    def __init__(self):
        """初始化匹配引擎"""
        self.logger = logging.getLogger(__name__)
        self.compiler = KeywordCompiler()
        
        # 从配置获取匹配规则
        self.matching_config = config_manager.get_dict('keyword_matching.matching_rules.keyword_matching')
        
        # 获取默认值
        self.like_default = self.matching_config.get('like_keyword_default', '0')
        self.must_default = self.matching_config.get('must_keyword_default', '0')
        self.unlike_default = self.matching_config.get('unlike_keyword_default', '0')
        self.match_tag_format = self.matching_config.get('match_tag_format', '_{type}_{original_keyword}')

        # 文本数据解析配置
        self.text_parsing_config = {
            'enable_json_parsing': True,
            'enable_delimiter_parsing': False,  # 已禁用自动分割功能
            'common_delimiters': ['|', '；', ';', '、', ',', '，'],  # 保留配置但不使用
            'min_text_length': 1,
            'max_text_length': 10000
        }

        # 性能优化配置 - 从配置文件读取
        text_processing_config = config_manager.get_dict('text_processing', {})
        self.performance_config = {
            'enable_text_length_precheck': text_processing_config.get('enable_text_length_precheck', True),
            'enable_pattern_cache': text_processing_config.get('enable_pattern_cache', True),
            'enable_batch_processing': text_processing_config.get('enable_batch_processing', True),
            'min_text_length_for_matching': text_processing_config.get('min_text_length_for_matching', 2),
            'max_text_length_for_matching': text_processing_config.get('max_text_length_for_matching', 5000),
            'pattern_cache_size': text_processing_config.get('pattern_cache_size', 1000)
        }

        # 性能优化缓存
        self._pattern_cache = {}
        self._cache_hit_count = 0
        self._cache_miss_count = 0

        self.logger.info("匹配引擎初始化完成")

    def _smart_parse_text_data(self, col_texts: Any) -> List[str]:
        """
        智能解析文本数据，支持多种格式的自动识别和转换

        支持的格式：
        1. 已经是列表格式：直接返回
        2. JSON字符串格式：如 '["文本1", "文本2"]'
        3. 单个字符串：转换为单元素列表

        注意：已移除自动分割功能，假设输入数据在预处理阶段已经处理完毕

        Args:
            col_texts: 原始文本数据，可能是字符串、列表或其他格式

        Returns:
            List[str]: 解析后的文本列表
        """
        # 如果已经是列表或数组，直接处理其中的元素（排除字符串）
        if isinstance(col_texts, (list, tuple)):
            # 处理标准的list和tuple
            result = []
            for item in col_texts:
                if item is not None:
                    item_str = str(item).strip()
                    if item_str:
                        result.append(item_str)
            return result
        elif hasattr(col_texts, '__iter__') and hasattr(col_texts, '__len__') and not isinstance(col_texts, (str, bytes)):
            # 处理numpy.ndarray和其他类似数组的对象（排除字符串和字节）
            try:
                result = []
                for item in col_texts:
                    if item is not None:
                        item_str = str(item).strip()
                        if item_str:
                            result.append(item_str)
                return result
            except Exception as e:
                self.logger.debug(f"处理数组类型数据时出错: {e}, 类型: {type(col_texts)}")
                # 回退到字符串处理
                pass

        # 处理空值情况（安全的空值检查）
        if col_texts is None:
            return []

        # 对于numpy数组等，检查长度
        try:
            if hasattr(col_texts, '__len__') and len(col_texts) == 0:
                return []
        except Exception:
            pass

        text_str = str(col_texts).strip()
        if not text_str:
            return []

        # 尝试JSON解析
        if self.text_parsing_config['enable_json_parsing']:
            try:
                parsed = json.loads(text_str)
                if isinstance(parsed, list):
                    result = []
                    for item in parsed:
                        if item and str(item).strip():
                            result.append(str(item).strip())
                    self.logger.debug(f"成功解析JSON格式文本数据，包含 {len(result)} 个元素")
                    return result
            except (json.JSONDecodeError, TypeError):
                # JSON解析失败，继续尝试其他方法
                pass

        # 默认情况：单个字符串（不进行分割）
        return [text_str]

    def _clean_and_validate_text(self, text_str: str) -> Optional[str]:
        """
        清理和验证单个文本字符串

        包含以下清理功能：
        1. HTML标签清理
        2. 特殊字符标准化
        3. 空白字符规范化
        4. 控制字符移除
        5. 文本长度验证

        Args:
            text_str: 原始文本字符串

        Returns:
            Optional[str]: 清理后的文本，如果无效则返回None
        """
        if not text_str:
            return None

        # 转换为字符串并基本清理
        cleaned = str(text_str).strip()
        if not cleaned:
            return None

        # 1. HTML标签清理
        cleaned = self._remove_html_tags(cleaned)

        # 2. 特殊字符标准化
        cleaned = self._normalize_special_characters(cleaned)

        # 3. 空白字符规范化
        cleaned = self._normalize_whitespace(cleaned)

        # 4. 控制字符移除
        cleaned = self._remove_control_characters(cleaned)

        # 最终清理
        cleaned = cleaned.strip()
        if not cleaned:
            return None

        # 5. 长度验证
        min_len = self.text_parsing_config['min_text_length']
        max_len = self.text_parsing_config['max_text_length']

        if len(cleaned) < min_len or len(cleaned) > max_len:
            self.logger.debug(f"文本长度不符合要求: {len(cleaned)} (要求: {min_len}-{max_len})")
            return None

        return cleaned

    def _remove_html_tags(self, text: str) -> str:
        """
        移除HTML标签

        Args:
            text: 包含HTML标签的文本

        Returns:
            str: 清理后的文本
        """
        # 移除HTML标签
        html_pattern = re.compile(r'<[^>]+>')
        cleaned = html_pattern.sub('', text)

        # 处理HTML实体
        html_entities = {
            '&nbsp;': ' ',
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&#39;': "'",
            '&hellip;': '...',
            '&mdash;': '—',
            '&ndash;': '–'
        }

        for entity, replacement in html_entities.items():
            cleaned = cleaned.replace(entity, replacement)

        return cleaned

    def _normalize_special_characters(self, text: str) -> str:
        """
        标准化特殊字符

        Args:
            text: 原始文本

        Returns:
            str: 标准化后的文本
        """
        # 中文标点符号标准化
        punctuation_map = {
            '，': ',',
            '。': '.',
            '；': ';',
            '：': ':',
            '？': '?',
            '！': '!',
            '（': '(',
            '）': ')',
            '【': '[',
            '】': ']',
            '《': '<',
            '》': '>',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '…': '...'
        }

        cleaned = text
        for chinese_punct, english_punct in punctuation_map.items():
            cleaned = cleaned.replace(chinese_punct, english_punct)

        return cleaned

    def _normalize_whitespace(self, text: str) -> str:
        """
        规范化空白字符

        Args:
            text: 原始文本

        Returns:
            str: 规范化后的文本
        """
        # 将多个连续的空白字符替换为单个空格
        whitespace_pattern = re.compile(r'\s+')
        cleaned = whitespace_pattern.sub(' ', text)

        return cleaned.strip()

    def _remove_control_characters(self, text: str) -> str:
        """
        移除控制字符

        Args:
            text: 原始文本

        Returns:
            str: 清理后的文本
        """
        # 移除ASCII控制字符（保留换行符和制表符）
        cleaned = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')

        return cleaned

    def _fast_text_precheck(self, text: str) -> bool:
        """
        快速文本预检查，过滤明显不符合条件的文本

        Args:
            text: 待检查的文本

        Returns:
            bool: True表示文本可能匹配，False表示可以跳过
        """
        if not self.performance_config['enable_text_length_precheck']:
            return True

        if not text or not text.strip():
            return False

        text_len = len(text)
        min_len = self.performance_config['min_text_length_for_matching']
        max_len = self.performance_config['max_text_length_for_matching']

        # 长度预检查
        if text_len < min_len or text_len > max_len:
            return False

        # 检查是否包含基本的中文或英文字符
        has_meaningful_content = any(
            '\u4e00' <= char <= '\u9fff' or  # 中文字符
            char.isalpha() or  # 英文字母
            char.isdigit()     # 数字
            for char in text
        )

        return has_meaningful_content

    def _get_cached_pattern(self, pattern_key: str, pattern_string: str) -> Optional[Any]:
        """
        获取缓存的编译模式，如果不存在则编译并缓存

        Args:
            pattern_key: 模式缓存键
            pattern_string: 模式字符串

        Returns:
            编译后的模式对象，如果编译失败返回None
        """
        if not self.performance_config['enable_pattern_cache']:
            # 如果禁用缓存，直接编译
            try:
                return self.compiler.compile_pattern(pattern_string)
            except Exception:
                return None

        # 检查缓存
        if pattern_key in self._pattern_cache:
            self._cache_hit_count += 1
            return self._pattern_cache[pattern_key]

        # 缓存未命中，编译新模式
        self._cache_miss_count += 1
        try:
            compiled_pattern = self.compiler.compile_pattern(pattern_string)

            # 检查缓存大小限制
            cache_size = self.performance_config['pattern_cache_size']
            if len(self._pattern_cache) >= cache_size:
                # 简单的LRU：移除第一个元素
                first_key = next(iter(self._pattern_cache))
                del self._pattern_cache[first_key]

            # 添加到缓存
            self._pattern_cache[pattern_key] = compiled_pattern
            return compiled_pattern

        except Exception as e:
            self.logger.debug(f"模式编译失败: {pattern_string}, 错误: {e}")
            return None

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计数据
        """
        total_requests = self._cache_hit_count + self._cache_miss_count
        hit_rate = (self._cache_hit_count / total_requests * 100) if total_requests > 0 else 0

        return {
            'pattern_cache_size': len(self._pattern_cache),
            'cache_hit_count': self._cache_hit_count,
            'cache_miss_count': self._cache_miss_count,
            'cache_hit_rate': f"{hit_rate:.2f}%",
            'total_cache_requests': total_requests
        }

    def match_keywords(self,
                      keyword_row: Dict[str, Any],
                      input_row: Dict[str, Any],
                      text_columns: List[str]) -> MatchResult:
        """
        执行关键词匹配 - 文本优先的完整三阶段匹配

        对每个文本值进行完整的like→must→unlike三阶段匹配，
        只有通过所有三个阶段的文本才被认为匹配成功。

        Args:
            keyword_row: 关键词规则行数据
            input_row: 输入数据行数据
            text_columns: 参与匹配的文本列列表

        Returns:
            MatchResult: 匹配结果
        """
        matched_texts = {col: [] for col in text_columns}
        match_details = {
            'total_texts_processed': 0,
            'texts_passed_all_stages': 0,
            'column_stats': {},  # 每列的统计信息
            'stage_stats': {
                'like_passed': 0,
                'must_passed': 0,
                'unlike_passed': 0,
                'like_failed': 0,
                'must_failed': 0,
                'unlike_failed': 0
            }
        }

        try:
            # 获取三个阶段的关键词规则
            like_keyword = keyword_row.get('converted_like_keyword', '')
            original_like = keyword_row.get('like_keyword', '')
            must_keyword = keyword_row.get('converted_must_keyword', '')
            original_must = keyword_row.get('must_keyword', '')
            unlike_keyword = keyword_row.get('converted_unlike_keyword', '')
            original_unlike = keyword_row.get('unlike_keyword', '')

            # 检查是否为默认值
            like_is_default = str(like_keyword).strip() == self.like_default
            must_is_default = str(must_keyword).strip() == self.must_default
            unlike_is_default = str(unlike_keyword).strip() == self.unlike_default

            # 对每个列进行处理
            for col in text_columns:
                col_texts = input_row.get(col, [])

                # 使用智能解析方法处理文本数据
                parsed_texts = self._smart_parse_text_data(col_texts)

                column_passed_count = 0
                column_total_count = len(parsed_texts)

                # 对列中的每个文本值进行完整的三阶段匹配
                for text in parsed_texts:
                    # 快速预检查，过滤明显不符合条件的文本
                    if not self._fast_text_precheck(text):
                        continue

                    # 清理和验证文本
                    cleaned_text = self._clean_and_validate_text(text)
                    if not cleaned_text:
                        continue

                    match_details['total_texts_processed'] += 1

                    # 对单个文本进行完整的三阶段匹配
                    text_passed, matched_keywords = self._match_single_text_complete(
                        cleaned_text,
                        like_keyword, original_like, like_is_default,
                        must_keyword, original_must, must_is_default,
                        unlike_keyword, original_unlike, unlike_is_default,
                        match_details
                    )

                    if text_passed:
                        # 生成综合匹配标记，显示实际匹配到的关键词
                        match_tag = self._generate_complete_match_tag(cleaned_text, matched_keywords)
                        matched_texts[col].append(match_tag)
                        column_passed_count += 1
                        match_details['texts_passed_all_stages'] += 1

                # 记录列级统计信息
                match_details['column_stats'][col] = {
                    'total_texts': column_total_count,
                    'passed_texts': column_passed_count,
                    'pass_rate': column_passed_count / column_total_count if column_total_count > 0 else 0
                }

            # 行级最终判断：只要任意一个列有文本通过完整匹配，整行就符合规则
            total_passed_texts = sum(len(texts) for texts in matched_texts.values())

            if total_passed_texts == 0:
                return MatchResult(
                    success=False,
                    matched_texts=matched_texts,
                    failure_reason="没有文本通过完整的三阶段匹配",
                    match_details=match_details
                )

            return MatchResult(
                success=True,
                matched_texts=matched_texts,
                match_details=match_details
            )
            
        except Exception as e:
            self.logger.error(f"关键词匹配过程中发生错误: {e}")
            return MatchResult(
                success=False,
                matched_texts=matched_texts,
                failure_reason=f"匹配过程异常: {e}",
                match_details=match_details
            )

    def _match_single_text_complete(self, text_str: str,
                                   like_keyword: str, original_like: str, like_is_default: bool,
                                   must_keyword: str, original_must: str, must_is_default: bool,
                                   unlike_keyword: str, original_unlike: str, unlike_is_default: bool,
                                   match_details: Dict[str, Any]) -> Tuple[bool, Dict[str, str]]:
        """
        对单个文本进行完整的三阶段匹配（like → must → unlike）

        Args:
            text_str: 要匹配的文本字符串
            like_keyword: like关键词规则
            original_like: 原始like关键词
            like_is_default: like是否为默认值
            must_keyword: must关键词规则
            original_must: 原始must关键词
            must_is_default: must是否为默认值
            unlike_keyword: unlike关键词规则
            original_unlike: 原始unlike关键词
            unlike_is_default: unlike是否为默认值
            match_details: 匹配详情统计

        Returns:
            Tuple[bool, Dict[str, str]]: (是否通过所有三个阶段, 实际匹配到的关键词)
        """
        matched_keywords = {'like': '', 'must': '', 'unlike': ''}

        # 阶段1：Like匹配
        if like_is_default:
            # 默认值自动通过
            like_passed = True
            matched_keywords['like'] = 'default'
        else:
            like_result = self._execute_keyword_match(like_keyword, original_like,
                                                    {'text': [text_str]}, ['text'], 'like')
            like_passed = like_result.success and len(like_result.matched_texts.get('text', [])) > 0

            if like_passed:
                # 提取实际匹配到的关键词
                matched_keywords['like'] = self._extract_matched_keyword(text_str, like_keyword)

        if like_passed:
            match_details['stage_stats']['like_passed'] += 1
        else:
            match_details['stage_stats']['like_failed'] += 1
            return False, matched_keywords  # Like阶段失败，直接返回

        # 阶段2：Must匹配
        if must_is_default:
            # 默认值自动通过
            must_passed = True
            matched_keywords['must'] = 'default'
        else:
            must_result = self._execute_keyword_match(must_keyword, original_must,
                                                    {'text': [text_str]}, ['text'], 'must')
            must_passed = must_result.success and len(must_result.matched_texts.get('text', [])) > 0

            if must_passed:
                # 提取实际匹配到的关键词
                matched_keywords['must'] = self._extract_matched_keyword(text_str, must_keyword)

        if must_passed:
            match_details['stage_stats']['must_passed'] += 1
        else:
            match_details['stage_stats']['must_failed'] += 1
            return False, matched_keywords  # Must阶段失败，直接返回

        # 阶段3：Unlike匹配
        if unlike_is_default:
            # 默认值自动通过
            unlike_passed = True
            matched_keywords['unlike'] = 'default'
        else:
            unlike_result = self._execute_keyword_match(unlike_keyword, original_unlike,
                                                      {'text': [text_str]}, ['text'], 'unlike')
            # Unlike逻辑相反：没有匹配才算通过
            unlike_passed = not unlike_result.success or len(unlike_result.matched_texts.get('text', [])) == 0

            if unlike_passed:
                matched_keywords['unlike'] = 'passed'  # Unlike通过表示没有匹配到

        if unlike_passed:
            match_details['stage_stats']['unlike_passed'] += 1
        else:
            match_details['stage_stats']['unlike_failed'] += 1
            return False, matched_keywords  # Unlike阶段失败，直接返回

        # 所有三个阶段都通过
        return True, matched_keywords

    def _extract_matched_keyword(self, text_str: str, keyword_rules: str) -> str:
        """
        从多个关键词规则中提取实际匹配到的具体关键词

        Args:
            text_str: 要匹配的文本字符串
            keyword_rules: 关键词规则字符串（可能包含多个规则）

        Returns:
            str: 实际匹配到的关键词
        """
        try:
            # 分割多个规则
            pattern_parts = self._safe_split_patterns(keyword_rules)

            for pattern_part in pattern_parts:
                pattern_part = pattern_part.strip()
                if pattern_part and pattern_part != '0':
                    # 编译模式
                    compiled_pattern = self.compiler.compile_keyword_pattern(pattern_part)
                    if compiled_pattern.is_valid:
                        # 检查是否匹配
                        if self._match_text_with_pattern(text_str, compiled_pattern):
                            # 提取具体的关键词
                            return self._extract_keyword_from_pattern(pattern_part, compiled_pattern)

            return "unknown"
        except Exception as e:
            self.logger.warning(f"提取匹配关键词失败: {e}")
            return "unknown"

    def _extract_keyword_from_pattern(self, pattern_part: str, compiled_pattern) -> str:
        """
        从编译后的模式中提取关键词

        Args:
            pattern_part: 原始模式字符串
            compiled_pattern: 编译后的模式

        Returns:
            str: 提取的关键词
        """
        try:
            # 如果有关键词列表，返回第一个关键词
            if compiled_pattern.keywords and len(compiled_pattern.keywords) > 0:
                return compiled_pattern.keywords[0]

            # 尝试从模式字符串中解析
            if pattern_part.startswith('[0,'):
                # 格式0: [0, "关键词"]
                import ast
                try:
                    parsed = ast.literal_eval(pattern_part)
                    if isinstance(parsed, list) and len(parsed) >= 2:
                        return str(parsed[1]).strip('"\'')
                except:
                    pass

            # 如果无法解析，返回模式字符串的简化版本
            return pattern_part.replace('[0, "', '').replace('"]', '').replace('"', '').strip()
        except Exception as e:
            self.logger.warning(f"从模式中提取关键词失败: {e}")
            return "unknown"

    def _generate_complete_match_tag(self, text_str: str, matched_keywords: Dict[str, str]) -> str:
        """
        生成完整匹配的标记，显示实际匹配到的具体关键词

        Args:
            text_str: 匹配的文本
            matched_keywords: 实际匹配到的关键词字典 {'like': '电动', 'must': '汽车', 'unlike': 'passed'}

        Returns:
            str: 完整匹配标记
        """
        try:
            # 构建标记后缀，显示实际匹配到的关键词
            stages = []

            if matched_keywords.get('like') and matched_keywords['like'] != 'default':
                stages.append(f"like_{matched_keywords['like']}")
            elif matched_keywords.get('like') == 'default':
                stages.append("like_default")

            if matched_keywords.get('must') and matched_keywords['must'] != 'default':
                stages.append(f"must_{matched_keywords['must']}")
            elif matched_keywords.get('must') == 'default':
                stages.append("must_default")

            if matched_keywords.get('unlike'):
                if matched_keywords['unlike'] == 'passed':
                    stages.append("unlike_passed")
                elif matched_keywords['unlike'] == 'default':
                    stages.append("unlike_default")

            if stages:
                tag_suffix = "_" + "_".join(stages)
            else:
                tag_suffix = "_complete_match"

            return f"{text_str}{tag_suffix}"
        except Exception as e:
            self.logger.warning(f"生成完整匹配标记失败: {e}")
            return f"{text_str}_complete_match"

    def _match_like_keywords(self, keyword_row: Dict[str, Any], input_row: Dict[str, Any], text_columns: List[str]) -> MatchResult:
        """匹配Like关键词"""
        like_keyword = keyword_row.get('converted_like_keyword', '')
        original_like = keyword_row.get('like_keyword', '')
        
        # 检查是否为默认值（自动通过）
        if str(like_keyword).strip() == self.like_default:
            return MatchResult(
                success=True,
                matched_texts={col: [] for col in text_columns},
                match_details={'auto_pass': True, 'reason': 'like_keyword为默认值'}
            )
        
        return self._execute_keyword_match(like_keyword, original_like, input_row, text_columns, 'like')
    
    def _match_must_keywords(self, keyword_row: Dict[str, Any], input_row: Dict[str, Any], text_columns: List[str]) -> MatchResult:
        """匹配Must关键词"""
        must_keyword = keyword_row.get('converted_must_keyword', '')
        original_must = keyword_row.get('must_keyword', '')
        
        # 检查是否为默认值（自动通过）
        if str(must_keyword).strip() == self.must_default:
            return MatchResult(
                success=True,
                matched_texts={col: [] for col in text_columns},
                match_details={'auto_pass': True, 'reason': 'must_keyword为默认值'}
            )
        
        return self._execute_keyword_match(must_keyword, original_must, input_row, text_columns, 'must')
    
    def _match_unlike_keywords(self, keyword_row: Dict[str, Any], input_row: Dict[str, Any], text_columns: List[str]) -> MatchResult:
        """匹配Unlike关键词"""
        unlike_keyword = keyword_row.get('converted_unlike_keyword', '')
        original_unlike = keyword_row.get('unlike_keyword', '')
        
        # 检查是否为默认值（自动通过）
        if str(unlike_keyword).strip() == self.unlike_default:
            return MatchResult(
                success=True,
                matched_texts={col: [] for col in text_columns},
                match_details={'auto_pass': True, 'reason': 'unlike_keyword为默认值'}
            )
        
        # Unlike关键词的逻辑相反：如果匹配到则失败
        match_result = self._execute_keyword_match(unlike_keyword, original_unlike, input_row, text_columns, 'unlike')
        
        # 检查是否有匹配
        has_matches = any(texts for texts in match_result.matched_texts.values())
        
        if has_matches:
            # 有匹配则失败
            return MatchResult(
                success=False,
                matched_texts=match_result.matched_texts,
                failure_reason="Unlike关键词匹配到内容",
                match_details=match_result.match_details
            )
        else:
            # 无匹配则成功
            return MatchResult(
                success=True,
                matched_texts={col: [] for col in text_columns},
                match_details={'no_match': True, 'reason': 'unlike_keyword未匹配到内容'}
            )
    
    def _execute_keyword_match(self, converted_keyword: str, original_keyword: str,
                              input_row: Dict[str, Any], text_columns: List[str],
                              match_type: str) -> MatchResult:
        """
        执行具体的关键词匹配

        Args:
            converted_keyword: 转换后的关键词字符串（可能包含"|"分隔的多个规则）
            original_keyword: 原始关键词字符串
            input_row: 输入数据行（字典格式，包含企业的各种文本字段）
            text_columns: 参与匹配的文本列名列表
            match_type: 匹配类型（'like', 'must', 'unlike'）

        Returns:
            MatchResult: 匹配结果，包含匹配文本和详细信息
        """
        matched_texts = {col: [] for col in text_columns}
        match_details = {
            'patterns_matched': 0,
            'total_texts_processed': 0,
            'valid_patterns': 0,
            'invalid_patterns': 0,
            'pattern_errors': []
        }

        try:
            # 步骤1：解析和分割多个规则（修复"|"分隔符处理问题）
            pattern_parts = self._safe_split_patterns(converted_keyword)
            all_compiled_patterns = []

            # 步骤2：编译每个规则为可执行的匹配模式
            for i, pattern_part in enumerate(pattern_parts):
                pattern_part = pattern_part.strip()
                if pattern_part and pattern_part != '0':
                    try:
                        compiled_pattern = self.compiler.compile_keyword_pattern(pattern_part)
                        if compiled_pattern.is_valid:
                            all_compiled_patterns.append(compiled_pattern)
                            match_details['valid_patterns'] += 1
                        else:
                            match_details['invalid_patterns'] += 1
                            match_details['pattern_errors'].append({
                                'index': i,
                                'pattern': pattern_part,
                                'error': compiled_pattern.error_message
                            })
                            self.logger.warning(f"无效的关键词模式 {i}: {pattern_part}, 错误: {compiled_pattern.error_message}")
                    except Exception as e:
                        match_details['invalid_patterns'] += 1
                        match_details['pattern_errors'].append({
                            'index': i,
                            'pattern': pattern_part,
                            'error': str(e)
                        })
                        self.logger.error(f"编译关键词模式 {i} 时发生异常: {pattern_part}, 错误: {e}")

            if not all_compiled_patterns:
                return MatchResult(
                    success=False,
                    matched_texts=matched_texts,
                    failure_reason="没有有效的关键词模式",
                    match_details=match_details
                )

            # 步骤3：对于每个text_columns中的列，提取和处理文本数据
            for col in text_columns:
                col_texts = input_row.get(col, [])

                # 使用智能解析方法处理文本数据（支持numpy.ndarray等格式）
                col_texts = self._smart_parse_text_data(col_texts)

                # 步骤4：在单个文本字符串上应用多个编译后的匹配模式
                for text in col_texts:
                    if not text or not str(text).strip():
                        continue

                    text_str = str(text).strip()
                    match_details['total_texts_processed'] += 1

                    # 检查是否匹配任一规则（OR逻辑实现）
                    text_matched = False
                    for compiled_pattern in all_compiled_patterns:
                        if self._match_text_with_pattern(text_str, compiled_pattern):
                            match_tag = self._generate_match_tag(text_str, original_keyword, match_type)
                            matched_texts[col].append(match_tag)
                            match_details['patterns_matched'] += 1
                            text_matched = True
                            break  # 匹配到一个规则即可（OR逻辑）

                    # 记录匹配详情
                    if text_matched:
                        self.logger.debug(f"文本匹配成功: {text_str[:50]}...")

            # 步骤5：判断匹配成功/失败的条件
            has_matches = any(texts for texts in matched_texts.values())

            if match_type in ['like', 'must'] and not has_matches:
                return MatchResult(
                    success=False,
                    matched_texts=matched_texts,
                    failure_reason=f"{match_type}关键词未找到匹配",
                    match_details=match_details
                )

            return MatchResult(
                success=True,
                matched_texts=matched_texts,
                match_details=match_details
            )

        except Exception as e:
            self.logger.error(f"执行关键词匹配时发生错误: {e}")
            return MatchResult(
                success=False,
                matched_texts=matched_texts,
                failure_reason=f"匹配执行异常: {e}",
                match_details=match_details
            )

    def _safe_split_patterns(self, converted_keyword: str) -> List[str]:
        """
        安全地分割包含多个规则的关键词字符串

        修复直接使用split('|')导致的问题，正确处理包含"|"的正则表达式内容

        Args:
            converted_keyword: 转换后的关键词字符串

        Returns:
            List[str]: 分割后的规则列表
        """
        if not converted_keyword or not isinstance(converted_keyword, str):
            return []

        converted_keyword = converted_keyword.strip()
        if not converted_keyword or converted_keyword == '0':
            return []

        # 如果不包含"|"，直接返回
        if '|' not in converted_keyword:
            return [converted_keyword]

        # 智能分割：考虑括号嵌套和引号
        patterns = []
        current_pattern = ""
        bracket_depth = 0
        in_quotes = False
        quote_char = None
        i = 0

        while i < len(converted_keyword):
            char = converted_keyword[i]

            # 处理引号
            if char in ['"', "'"] and (i == 0 or converted_keyword[i-1] != '\\'):
                if not in_quotes:
                    in_quotes = True
                    quote_char = char
                elif char == quote_char:
                    in_quotes = False
                    quote_char = None

            # 处理括号
            elif not in_quotes:
                if char == '[':
                    bracket_depth += 1
                elif char == ']':
                    bracket_depth -= 1
                elif char == '|' and bracket_depth == 0:
                    # 只有在不在引号内且括号平衡时才分割
                    if current_pattern.strip():
                        patterns.append(current_pattern.strip())
                    current_pattern = ""
                    i += 1
                    continue

            current_pattern += char
            i += 1

        # 添加最后一个模式
        if current_pattern.strip():
            patterns.append(current_pattern.strip())

        return patterns

    def _match_text_with_pattern(self, text: str, compiled_pattern: CompiledPattern) -> bool:
        """
        使用编译后的模式匹配文本
        
        Args:
            text: 要匹配的文本
            compiled_pattern: 编译后的模式
            
        Returns:
            bool: 是否匹配成功
        """
        if not compiled_pattern.regex_pattern:
            return False
        
        try:
            match = compiled_pattern.regex_pattern.search(text)
            return match is not None
        except Exception as e:
            self.logger.warning(f"正则表达式匹配失败: {e}")
            return False
    
    def _generate_match_tag(self, matched_text: str, original_keyword: str, match_type: str) -> str:
        """
        生成匹配标记
        
        Args:
            matched_text: 匹配的文本
            original_keyword: 原始关键词
            match_type: 匹配类型
            
        Returns:
            str: 匹配标记
        """
        try:
            tag_suffix = self.match_tag_format.format(
                type=match_type,
                original_keyword=original_keyword
            )
            return f"{matched_text}{tag_suffix}"
        except Exception as e:
            self.logger.warning(f"生成匹配标记失败: {e}")
            return f"{matched_text}_{match_type}_{original_keyword}"
    
    def get_compiler_stats(self) -> Dict[str, Any]:
        """获取编译器统计信息"""
        return self.compiler.get_cache_info()


def test_matching_engine():
    """测试匹配引擎"""
    print("=" * 60)
    print("匹配引擎测试")
    print("=" * 60)

    engine = MatchingEngine()

    # 测试数据1：基本匹配
    print("\n测试1：基本匹配")
    keyword_row1 = {
        'converted_like_keyword': '[0, "新能源"]',
        'like_keyword': '新能源',
        'converted_must_keyword': '[0, "汽车"]',
        'must_keyword': '汽车',
        'converted_unlike_keyword': '0',
        'unlike_keyword': ''
    }

    input_row1 = {
        'company_profile': ['我们是一家专业的新能源汽车制造企业'],
        'main_product': ['电动汽车', '充电桩设备'],
        'business_scope': ['传统燃油车销售']
    }

    text_columns = ['company_profile', 'main_product', 'business_scope']

    result1 = engine.match_keywords(keyword_row1, input_row1, text_columns)
    print(f"匹配结果: {result1.success}")
    if result1.failure_reason:
        print(f"失败原因: {result1.failure_reason}")
    print("匹配文本:")
    for col, texts in result1.matched_texts.items():
        if texts:
            print(f"  {col}: {texts}")


def test_multi_value_column_matching():
    """专门测试多值列的匹配逻辑"""
    print("\n" + "=" * 80)
    print("多值列匹配逻辑详细测试")
    print("=" * 80)

    engine = MatchingEngine()

    # 测试场景1：多值列中部分匹配
    print("\n【测试场景1】多值列中部分匹配")
    print("数据: main_product = ['电动车', '混合动力车', '燃油车']")
    print("规则: like=[0, '电动'], must=[0, '车'], unlike=[0, '燃油']")

    keyword_row = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "车"]',
        'must_keyword': '车',
        'converted_unlike_keyword': '[0, "燃油"]',
        'unlike_keyword': '燃油'
    }

    input_row = {
        'main_product': ['电动车', '混合动力车', '燃油车']
    }

    text_columns = ['main_product']

    # 分别测试每个匹配阶段
    print("\n--- Like匹配阶段 ---")
    like_result = engine._match_like_keywords(keyword_row, input_row, text_columns)
    print(f"Like匹配结果: {like_result.success}")
    print(f"Like匹配文本: {like_result.matched_texts}")

    print("\n--- Must匹配阶段 ---")
    must_result = engine._match_must_keywords(keyword_row, input_row, text_columns)
    print(f"Must匹配结果: {must_result.success}")
    print(f"Must匹配文本: {must_result.matched_texts}")

    print("\n--- Unlike匹配阶段 ---")
    unlike_result = engine._match_unlike_keywords(keyword_row, input_row, text_columns)
    print(f"Unlike匹配结果: {unlike_result.success}")
    print(f"Unlike匹配文本: {unlike_result.matched_texts}")
    if unlike_result.failure_reason:
        print(f"Unlike失败原因: {unlike_result.failure_reason}")

    print("\n--- 整体匹配结果 ---")
    final_result = engine.match_keywords(keyword_row, input_row, text_columns)
    print(f"最终匹配结果: {final_result.success}")
    if final_result.failure_reason:
        print(f"失败原因: {final_result.failure_reason}")
    print(f"最终匹配文本: {final_result.matched_texts}")

    # 测试场景2：多值列全部不匹配
    print("\n【测试场景2】多值列全部不匹配")
    print("数据: main_product = ['传统设备', '机械产品']")
    print("规则: like=[0, '电动'], must=[0, '汽车']")

    keyword_row2 = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "汽车"]',
        'must_keyword': '汽车',
        'converted_unlike_keyword': '0',
        'unlike_keyword': ''
    }

    input_row2 = {
        'main_product': ['传统设备', '机械产品']
    }

    result2 = engine.match_keywords(keyword_row2, input_row2, text_columns)
    print(f"匹配结果: {result2.success}")
    if result2.failure_reason:
        print(f"失败原因: {result2.failure_reason}")
    print(f"匹配文本: {result2.matched_texts}")

    # 测试场景3：多值列全部匹配
    print("\n【测试场景3】多值列全部匹配")
    print("数据: main_product = ['电动汽车', '电动摩托车']")
    print("规则: like=[0, '电动'], must=[0, '车']")

    keyword_row3 = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "车"]',
        'must_keyword': '车',
        'converted_unlike_keyword': '0',
        'unlike_keyword': ''
    }

    input_row3 = {
        'main_product': ['电动汽车', '电动摩托车']
    }

    result3 = engine.match_keywords(keyword_row3, input_row3, text_columns)
    print(f"匹配结果: {result3.success}")
    print(f"匹配文本: {result3.matched_texts}")

    print(f"\n编译器统计: {engine.get_compiler_stats()}")


def test_text_priority_matching():
    """测试文本优先的完整三阶段匹配逻辑"""
    print("\n" + "=" * 80)
    print("文本优先完整三阶段匹配测试")
    print("=" * 80)

    engine = MatchingEngine()

    # 测试场景1：部分文本通过完整匹配
    print("\n【测试场景1】部分文本通过完整匹配")
    print("数据: main_product = ['电动车', '混合动力车', '燃油车']")
    print("规则: like=[0, '电动'], must=[0, '车'], unlike=[0, '燃油']")
    print("预期: 只有'电动车'通过所有三阶段，'燃油车'在unlike阶段失败")

    keyword_row1 = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "车"]',
        'must_keyword': '车',
        'converted_unlike_keyword': '[0, "燃油"]',
        'unlike_keyword': '燃油'
    }

    input_row1 = {
        'main_product': ['电动车', '混合动力车', '燃油车']
    }

    text_columns = ['main_product']

    result1 = engine.match_keywords(keyword_row1, input_row1, text_columns)
    print(f"\n匹配结果: {result1.success}")
    if result1.failure_reason:
        print(f"失败原因: {result1.failure_reason}")
    print(f"匹配文本: {result1.matched_texts}")
    print(f"详细统计: {result1.match_details}")

    # 测试场景2：所有文本都无法通过完整匹配
    print("\n【测试场景2】所有文本都无法通过完整匹配")
    print("数据: main_product = ['传统设备', '机械产品']")
    print("规则: like=[0, '电动'], must=[0, '汽车']")
    print("预期: 所有文本在like阶段就失败")

    keyword_row2 = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "汽车"]',
        'must_keyword': '汽车',
        'converted_unlike_keyword': '0',
        'unlike_keyword': ''
    }

    input_row2 = {
        'main_product': ['传统设备', '机械产品']
    }

    result2 = engine.match_keywords(keyword_row2, input_row2, text_columns)
    print(f"\n匹配结果: {result2.success}")
    if result2.failure_reason:
        print(f"失败原因: {result2.failure_reason}")
    print(f"匹配文本: {result2.matched_texts}")
    print(f"详细统计: {result2.match_details}")

    # 测试场景3：多列多文本的复杂场景
    print("\n【测试场景3】多列多文本的复杂场景")
    print("数据: main_product = ['电动汽车', '燃油汽车'], service_intro = ['新能源技术', '传统维修']")
    print("规则: like=[0, '电动']|[0, '新能源'], must=[0, '汽车']|[0, '技术'], unlike=[0, '燃油']")
    print("预期: main_product中'电动汽车'通过，service_intro中'新能源技术'通过")

    keyword_row3 = {
        'converted_like_keyword': '[0, "电动"]|[0, "新能源"]',
        'like_keyword': '电动或新能源',
        'converted_must_keyword': '[0, "汽车"]|[0, "技术"]',
        'must_keyword': '汽车或技术',
        'converted_unlike_keyword': '[0, "燃油"]',
        'unlike_keyword': '燃油'
    }

    input_row3 = {
        'main_product': ['电动汽车', '燃油汽车'],
        'service_intro': ['新能源技术', '传统维修']
    }

    text_columns3 = ['main_product', 'service_intro']

    result3 = engine.match_keywords(keyword_row3, input_row3, text_columns3)
    print(f"\n匹配结果: {result3.success}")
    if result3.failure_reason:
        print(f"失败原因: {result3.failure_reason}")
    print(f"匹配文本: {result3.matched_texts}")
    print(f"详细统计: {result3.match_details}")

    print(f"\n编译器统计: {engine.get_compiler_stats()}")


if __name__ == "__main__":
    test_matching_engine()
    test_multi_value_column_matching()
    test_text_priority_matching()


