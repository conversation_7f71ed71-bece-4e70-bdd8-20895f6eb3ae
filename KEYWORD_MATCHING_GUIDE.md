# 智能关键词匹配功能使用指南

## 功能概述

智能关键词匹配功能（choice=4）是挂链工具集v2.0的核心功能之一，基于已有的转换后关键词表格和输入数据表格，实现高效的文本匹配和标记功能。

## 核心特性

### 🎯 智能匹配逻辑
- **三步匹配流程**：Like → Must → Unlike 关键词匹配
- **多种匹配模式**：支持直接匹配、有序匹配、无序匹配
- **灵活筛选机制**：行业类型筛选 + 数据源范围筛选

### 🚀 性能优化
- **正则表达式预编译**：关键词模式预编译并缓存
- **批量并行处理**：支持多线程并行处理大数据集
- **内存优化管理**：合理控制内存使用，避免溢出

### 📊 详细报告
- **匹配结果文件**：CSV格式，包含所有匹配成功的记录
- **统计摘要报告**：JSON格式，提供详细的匹配统计信息

## 数据结构要求

### 关键词表格（df_keyword）
必须包含以下列：

**标签列**：
- `chain_name` - 产业链名称
- `chain_level_1_name` - 一级产业链名称
- `chain_level_2_name` - 二级产业链名称
- `chain_level_3_name` - 三级产业链名称
- `chain_level_4_name` - 四级产业链名称

**筛选列**：
- `industry_type` - 行业筛选（"default"表示跳过筛选）
- `source_scope` - 数据源筛选（"default"表示包含所有列）

**转换后关键词列**：
- `converted_like_keyword` - 转换后的Like关键词
- `converted_must_keyword` - 转换后的Must关键词
- `converted_unlike_keyword` - 转换后的Unlike关键词

### 输入数据表格（input_df）
必须包含以下列：

**标识列**：
- `company_id` - 企业唯一标识
- `company_name` - 企业名称

**筛选列**：
- `industry_l1_code` - 行业代码（用于行业筛选）

**文本内容列**（以列表形式存储）：
- `company_profile` - 企业简介
- `introduction` - 企业介绍
- `main_product` - 主要产品
- `service_intro` - 服务介绍
- `business_scope` - 经营范围
- 等等...（详见配置文件）

## 匹配逻辑详解

### 第一层：行业类型筛选
```
if industry_type == "default":
    跳过行业筛选
else:
    检查 industry_l1_code 是否在 industry_type 的分割列表中
```

### 第二层：数据源范围筛选
```
if source_scope == "default":
    所有文本列都参与匹配
else:
    根据 except_ 前缀排除相应的文本列
    - "except_software" → 排除 software_full_name
    - "except_patent" → 排除 patent_name, patent_description
```

### 第三层：关键词匹配
按顺序进行三步匹配：

1. **Like关键词匹配**
   - 如果 `converted_like_keyword == "0"`：自动通过
   - 否则：检查关键词是否在文本中存在

2. **Must关键词匹配**
   - 如果 `converted_must_keyword == "0"`：自动通过
   - 否则：检查关键词是否在文本中存在

3. **Unlike关键词匹配**
   - 如果 `converted_unlike_keyword == "0"`：自动通过
   - 如果关键词在文本中存在：匹配失败并终止

## 使用方法

### 1. 通过主程序启动
```bash
python main.py
# 选择 3. 智能关键词匹配
```

### 2. 直接运行CLI
```bash
python cli/keyword_matcher_cli.py
```

### 3. 程序化调用
```python
from core.keyword_matcher import KeywordMatcher

matcher = KeywordMatcher()
result_file, report_file = matcher.run_complete_matching(
    input_file_path="your_input_data.xlsx",
    keyword_file_path="converted_keyword_rules.xlsx",
    output_file_path="matching_results.csv"
)
```

## 配置参数

在 `config.json` 中可以调整以下参数：

### 性能配置
```json
{
  "keyword_matching": {
    "enable_performance_optimization": true,
    "batch_size": 1000,
    "max_workers": 4
  }
}
```

### 文件路径配置
```json
{
  "keyword_matching": {
    "keyword_file_path": "auto_detect",
    "input_file_path": "",
    "output_file_path": "matching_results.csv"
  }
}
```

### 匹配规则配置
```json
{
  "keyword_matching": {
    "matching_rules": {
      "industry_filter": {
        "enable": true,
        "default_value": "default",
        "separator": "、"
      },
      "source_scope_filter": {
        "enable": true,
        "default_value": "default",
        "except_mappings": {
          "except_software": ["software_full_name"],
          "except_patent": ["patent_name", "patent_description"]
        }
      }
    }
  }
}
```

## 输出格式

### 匹配结果文件（CSV）
```
keyword_index,company_id,company_name,company_profile_matched_texts,main_product_matched_texts,...
0,C001,新能源汽车公司,"[""新能源汽车制造_like_新能源""]","[""电动汽车_must_汽车""]",...
```

### 摘要报告文件（JSON）
```json
{
  "total_processed": 1000,
  "successful_matches": 150,
  "failed_matches": 850,
  "match_rate": 0.15,
  "column_match_stats": {
    "company_profile": {"matches": 120, "rate": 0.8},
    "main_product": {"matches": 80, "rate": 0.53}
  },
  "processing_stats": {
    "processing_time": 45.2,
    "compiled_count": 500,
    "cache_hits": 1200
  }
}
```

## 性能建议

### 大数据集处理
- 启用性能优化：`enable_performance_optimization: true`
- 调整批处理大小：`batch_size: 1000-5000`
- 增加工作线程：`max_workers: 4-8`

### 内存优化
- 分批处理大文件
- 定期清理编译缓存
- 监控内存使用情况

## 故障排除

### 常见问题

1. **文件格式错误**
   - 确保关键词文件包含所有必需列
   - 检查文本内容列是否为列表格式

2. **匹配结果为空**
   - 检查行业筛选配置
   - 验证关键词转换是否正确

3. **性能问题**
   - 减少批处理大小
   - 检查正则表达式复杂度

### 调试模式
启用详细日志：
```json
{
  "keyword_matching": {
    "enable_logging": true
  }
}
```

## 扩展开发

### 添加新的筛选规则
1. 在 `core/filter_logic.py` 中添加新的筛选方法
2. 更新配置文件中的筛选配置
3. 编写相应的单元测试

### 自定义匹配模式
1. 在 `core/keyword_compiler.py` 中添加新的编译方法
2. 在 `core/matching_engine.py` 中添加匹配逻辑
3. 更新数据模型和测试

## 技术支持

如遇到问题，请：
1. 查看日志文件：`keyword_matching_YYYYMMDD.log`
2. 运行单元测试：`python tests/test_keyword_matching.py`
3. 检查配置文件格式和内容

---

**智能关键词匹配功能** - 让企业数据产业链标记更智能、更高效！
