# 挂链工具集 v2.0

一个专业的关键词规则验证和模式转换工具集，用于处理复杂的关键词规则验证、模式转换和数据提取任务。

## 🚀 功能特色

### 核心功能
- **关键词规则验证器**：支持10种不同的关键词组合规则模式，自动验证Excel文件中的关键词格式
- **关键词模式转换器**：将关键词规则转换为正则表达式匹配格式，支持有序匹配和无序匹配
- **智能关键词匹配器**：基于转换后的关键词规则，对企业数据进行智能匹配和产业链标记
- **数据验证层**：基于Pydantic的强类型数据验证，确保数据完整性和正确性
- **单元测试框架**：完整的测试覆盖，确保代码质量和可靠性

### 技术特性
- **模块化设计**：清晰的代码架构，易于维护和扩展
- **类型安全**：使用Pydantic进行数据验证和类型检查
- **错误处理**：完善的异常处理和错误报告机制
- **日志记录**：详细的操作日志，便于调试和监控
- **配置管理**：统一的配置文件管理，支持环境变量

## 📁 项目结构

```
v2.0/
├── cli/                          # CLI工具模块
│   ├── __init__.py
│   ├── keyword_rules_validator_cli.py    # 关键词规则验证CLI
│   ├── keyword_converter_cli.py          # 关键词转换CLI
│   └── keyword_matcher_cli.py            # 智能关键词匹配CLI
├── core/                         # 核心业务逻辑
│   ├── __init__.py
│   ├── pattern_transformer.py           # 模式转换器
│   ├── data_models.py                   # 数据模型和验证
│   ├── keyword_compiler.py             # 关键词编译器
│   ├── filter_logic.py                 # 筛选逻辑模块
│   ├── matching_engine.py              # 匹配引擎模块
│   ├── result_processor.py             # 结果处理模块
│   └── keyword_matcher.py              # 主控制模块
├── tests/                        # 单元测试
│   ├── __init__.py              
│   ├── test_pattern_transformer.py      # 模式转换器测试
│   ├── test_data_models.py             # 数据模型测试
│   └── test_runner.py                  # 测试运行器
├── config/                       # 配置管理
│   ├── __init__.py              
│   ├── config.json              # 主配置文件
│   └── manager.py               # 配置管理器
├── main.py                      # 主程序入口
├── requirements.txt             # 项目依赖
└── README.md                    # 项目说明

```

## 🛠️ 安装使用

### 环境要求
- Python 3.8+
- pandas >= 1.3.0
- openpyxl >= 3.0.0
- pydantic >= 1.8.0

### 安装依赖
```bash
pip install -r requirements.txt
```

### 快速开始
```bash
# 启动主菜单
python main.py

# 直接运行关键词规则验证
python cli/keyword_rules_validator_cli.py

# 直接运行关键词模式转换
python cli/keyword_converter_cli.py

# 直接运行智能关键词匹配
python cli/keyword_matcher_cli.py

# 运行单元测试
python tests/test_runner.py

# 运行特定测试模块
python tests/test_runner.py --module test_pattern_transformer

# 运行代码覆盖率测试
python tests/test_runner.py --coverage
```

## 📋 功能详解

### 1. 关键词规则验证器

验证Excel文件中的关键词是否符合预定义的规则模式：

#### 支持的规则模式
1. **规则1**: `A&B` - 两个关键词用&连接
2. **规则2**: `A&&B` - 两个关键词用&&连接
3. **规则3**: `A&(B,C,D)` - 单个关键词与括号内多个关键词组合
4. **规则4**: `(B,C,D)&A` - 括号内多个关键词与单个关键词组合
5. **规则5**: `A&&(B,C,D)` - 使用&&的单个与组合
6. **规则6**: `(B,C,D)&&A` - 使用&&的组合与单个
7. **规则7**: `(A,B)&(C,D)` - 两组括号关键词的&组合
8. **规则8**: `(A,B)&&(C,D)` - 两组括号关键词的&&组合
9. **规则9**: `A.{0,X}B` - 正则表达式距离限制模式
10. **规则10**: `A` - 单个关键词

#### 功能特点
- 自动处理中文符号转换（，→, ，（→(，）→)）
- 支持多值验证（使用|分隔）
- 错误单元格高亮标记
- 生成详细的验证报告

### 2. 关键词模式转换器

将关键词规则转换为结构化的匹配格式：

#### 转换格式说明
- **格式0**: `[0, "关键词"]` - 单个关键词直接匹配
- **格式1**: `[1, ["左关键词"], ["右关键词"], 最小间隔, 最大间隔]` - 有序匹配
- **格式2**: `[2, ["关键词组1"], ["关键词组2"], 最小间隔, 最大间隔]` - 无序匹配

#### 支持的输入模式
- `汽车&保险` → `[1, ["汽车"], ["保险"], 0, 200]`
- `汽车&&保险` → `[2, ["汽车"], ["保险"], 0, 200]`
- `车载.{0,5}充电机` → `[1, ["车载"], ["充电机"], 0, 5]`
- `光刻机&(研发,制造,销售)` → `[1, ["光刻机"], ["研发", "制造", "销售"], 0, 200]`

### 3. 智能关键词匹配器

基于转换后的关键词规则表，对企业数据进行智能匹配和产业链标记：

#### 核心特性
- **三步匹配流程**：Like → Must → Unlike 关键词匹配
- **智能筛选机制**：行业类型筛选 + 数据源范围筛选
- **高性能处理**：正则表达式预编译、批量并行处理
- **详细报告**：匹配结果文件 + 统计摘要报告

#### 匹配逻辑
1. **行业类型筛选**：基于industry_type和industry_l1_code进行匹配
2. **数据源范围筛选**：基于source_scope控制参与匹配的文本列
3. **Like关键词匹配**：检查是否存在，记录匹配信息
4. **Must关键词匹配**：检查是否存在，记录匹配信息
5. **Unlike关键词匹配**：检查是否存在，如存在则匹配失败

#### 输出格式
- **匹配结果文件**：CSV格式，包含所有匹配成功的记录
- **摘要报告文件**：JSON格式，提供详细的匹配统计信息

详细使用说明请参考：[智能关键词匹配功能使用指南](KEYWORD_MATCHING_GUIDE.md)

## 🧪 测试框架

### 单元测试
项目包含完整的单元测试框架，覆盖所有核心功能：

```bash
# 运行所有测试
python tests/test_runner.py

# 运行特定测试类
python tests/test_runner.py --module test_pattern_transformer --class TestPatternTransformer

# 运行特定测试方法
python tests/test_runner.py --module test_pattern_transformer --class TestPatternTransformer --method test_single_keyword_pattern

# 详细模式
python tests/test_runner.py --verbose

# 静默模式
python tests/test_runner.py --quiet
```

### 代码覆盖率
```bash
# 生成覆盖率报告
python tests/test_runner.py --coverage

# 查看HTML覆盖率报告
# 报告将生成在 htmlcov/ 目录中
```

## 🔧 数据验证层

使用Pydantic实现的强类型数据验证：

### 数据模型
- **PatternType**: 模式类型枚举
- **KeywordPattern**: 关键词模式数据模型
- **TransformResult**: 转换结果数据模型
- **ConversionConfig**: 转换配置数据模型
- **ValidationConfig**: 验证配置数据模型

### 验证特性
- 类型安全检查
- 数据完整性验证
- 自动数据转换
- 详细的错误报告

## ⚙️ 配置参数

### 关键词规则验证配置
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `excel.file_path` | Excel文件路径 | - |
| `excel.sheet_name` | 工作表名称 | "Sheet1" |
| `keyword_columns` | 要验证的关键词列 | - |
| `validation_rules.highlight_color` | 高亮颜色 | "FFFF00" |
| `validation_rules.max_errors_display` | 最大错误显示数量 | 10 |

### 模式转换配置
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `pattern_conversion.max_chars` | 默认最大字符间隔 | 200 |
| `pattern_conversion.source_file_path` | 源文件路径 | "auto_detect" |
| `pattern_conversion.output_file_path` | 输出文件路径 | "converted_keyword_rules.xlsx" |
| `pattern_conversion.output_sheet_name` | 输出工作表名称 | "转换后规则" |
| `pattern_conversion.column_prefix` | 列名前缀 | "converted" |
| `pattern_conversion.enable_logging` | 是否启用日志 | true |

## 🏗️ 开发指南

### 代码规范
- 使用Python类型提示
- 遵循PEP 8代码风格
- 编写完整的文档字符串
- 保持函数功能单一

### 添加新功能
1. 在`core/`目录下实现核心逻辑
2. 在`cli/`目录下创建CLI接口
3. 在`tests/`目录下编写单元测试
4. 更新配置文件和文档

### 测试覆盖
- 所有新功能必须包含单元测试
- 测试覆盖率应保持在90%以上
- 使用测试驱动开发(TDD)方法

## 📈 版本历史

### v2.0 (当前版本)
- ✨ 添加数据验证层（Pydantic）
- ✨ 完整的单元测试框架
- ✨ 项目结构优化重构
- ✨ 类型安全和错误处理改进
- ✨ 代码覆盖率测试支持
- 🐛 修复多种边缘情况

### v1.0
- ✅ 关键词规则验证功能
- ✅ 关键词模式转换功能
- ✅ 基础配置管理
- ✅ CLI交互界面

## 🤝 贡献指南

1. Fork本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看LICENSE文件了解详情

## 📞 支持与反馈

如果您在使用过程中遇到任何问题或有功能建议，请：
1. 查看文档和示例
2. 搜索已有的Issues
3. 创建新的Issue描述问题

## 🔮 未来规划

- [x] 智能关键词匹配器 ✅
- [ ] Web界面支持
- [ ] 更多文件格式支持
- [ ] 性能优化和缓存机制
- [ ] 国际化支持
- [ ] API服务模式

---

**挂链工具集** - 让关键词处理更简单、更可靠！ 