#!/usr/bin/env python3
"""
关键词匹配功能单元测试
Keyword Matching Unit Tests

该模块包含智能关键词匹配功能的完整单元测试，
覆盖所有核心模块和功能组件。

测试覆盖：
1. 关键词编译器测试
2. 筛选逻辑测试
3. 匹配引擎测试
4. 结果处理器测试
5. 主控制器测试

作者：系统开发
日期：2024年
"""

import unittest
import pandas as pd
import tempfile
import json
from pathlib import Path
import sys
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.keyword_compiler import KeywordCompiler, CompiledPattern
from core.filter_logic import FilterLogic, FilterResult
from core.matching_engine import MatchingEngine, MatchResult
from core.result_processor import ResultProcessor
from core.keyword_matcher import KeywordMatcher


class TestKeywordCompiler(unittest.TestCase):
    """关键词编译器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.compiler = KeywordCompiler()
    
    def test_compile_direct_match(self):
        """测试直接匹配模式编译"""
        pattern_data = [0, "新能源汽车"]
        result = self.compiler.compile_keyword_pattern(pattern_data)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.pattern_type, 0)
        self.assertIsNotNone(result.regex_pattern)
        self.assertEqual(result.keywords, ["新能源汽车"])
    
    def test_compile_ordered_match(self):
        """测试有序匹配模式编译"""
        pattern_data = [1, ["新能源"], ["汽车"], 0, 10]
        result = self.compiler.compile_keyword_pattern(pattern_data)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.pattern_type, 1)
        self.assertIsNotNone(result.regex_pattern)
        self.assertEqual(result.keywords, ["新能源", "汽车"])
    
    def test_compile_unordered_match(self):
        """测试无序匹配模式编译"""
        pattern_data = [2, ["电动", "混动"], ["汽车", "车辆"], 0, 20]
        result = self.compiler.compile_keyword_pattern(pattern_data)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.pattern_type, 2)
        self.assertIsNotNone(result.regex_pattern)
        self.assertEqual(result.keywords, ["电动", "混动", "汽车", "车辆"])
    
    def test_compile_invalid_pattern(self):
        """测试无效模式编译"""
        pattern_data = [99, "invalid"]
        result = self.compiler.compile_keyword_pattern(pattern_data)
        
        self.assertFalse(result.is_valid)
        self.assertIsNotNone(result.error_message)
    
    def test_compile_string_pattern(self):
        """测试字符串格式模式编译"""
        pattern_data = "[0, '测试关键词']"
        result = self.compiler.compile_keyword_pattern(pattern_data)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.pattern_type, 0)
    
    def test_compile_zero_pattern(self):
        """测试零值模式"""
        pattern_data = "0"
        result = self.compiler.compile_keyword_pattern(pattern_data)
        
        self.assertFalse(result.is_valid)
    
    def test_batch_compile(self):
        """测试批量编译"""
        patterns = [
            [0, "关键词1"],
            [0, "关键词2"],
            "0"
        ]
        results = self.compiler.batch_compile(patterns)
        
        self.assertEqual(len(results), 3)
        self.assertTrue(results[0].is_valid)
        self.assertTrue(results[1].is_valid)
        self.assertFalse(results[2].is_valid)


class TestFilterLogic(unittest.TestCase):
    """筛选逻辑测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.filter_logic = FilterLogic()
    
    def test_industry_filter_default(self):
        """测试行业筛选默认值"""
        result = self.filter_logic.apply_industry_filter("default", "123")
        self.assertTrue(result.passed)
        self.assertIn("默认", result.reason)
    
    def test_industry_filter_match(self):
        """测试行业筛选匹配"""
        result = self.filter_logic.apply_industry_filter("123、456、789", "456")
        self.assertTrue(result.passed)
        self.assertIn("匹配", result.reason)
    
    def test_industry_filter_no_match(self):
        """测试行业筛选不匹配"""
        result = self.filter_logic.apply_industry_filter("123、456", "999")
        self.assertFalse(result.passed)
        self.assertIn("不匹配", result.reason)
    
    def test_source_scope_filter_default(self):
        """测试数据源筛选默认值"""
        result = self.filter_logic.apply_source_scope_filter("default")
        self.assertTrue(result.passed)
        self.assertIsNotNone(result.filtered_columns)
    
    def test_source_scope_filter_except_software(self):
        """测试数据源筛选排除软件"""
        result = self.filter_logic.apply_source_scope_filter("except_software")
        self.assertTrue(result.passed)
        self.assertNotIn("software_full_name", result.filtered_columns)
    
    def test_source_scope_filter_except_patent(self):
        """测试数据源筛选排除专利"""
        result = self.filter_logic.apply_source_scope_filter("except_patent")
        self.assertTrue(result.passed)
        self.assertNotIn("patent_name", result.filtered_columns)
        self.assertNotIn("patent_description", result.filtered_columns)
    
    def test_combined_filter(self):
        """测试组合筛选"""
        keyword_row = {
            'industry_type': '123、456',
            'source_scope': 'except_software'
        }
        input_row = {
            'industry_l1_code': '456'
        }
        
        result = self.filter_logic.apply_combined_filter(keyword_row, input_row)
        self.assertTrue(result.passed)
        self.assertIsNotNone(result.filtered_columns)


class TestMatchingEngine(unittest.TestCase):
    """匹配引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.engine = MatchingEngine()
    
    def test_match_like_keywords_default(self):
        """测试Like关键词默认值匹配"""
        keyword_row = {
            'converted_like_keyword': '0',
            'like_keyword': ''
        }
        input_row = {'company_profile': ['测试文本']}
        text_columns = ['company_profile']
        
        result = self.engine._match_like_keywords(keyword_row, input_row, text_columns)
        self.assertTrue(result.success)
    
    def test_match_like_keywords_success(self):
        """测试Like关键词成功匹配"""
        keyword_row = {
            'converted_like_keyword': '[0, "新能源"]',
            'like_keyword': '新能源'
        }
        input_row = {'company_profile': ['我们是新能源汽车公司']}
        text_columns = ['company_profile']
        
        result = self.engine._match_like_keywords(keyword_row, input_row, text_columns)
        self.assertTrue(result.success)
        self.assertTrue(any(result.matched_texts.values()))
    
    def test_match_unlike_keywords_fail(self):
        """测试Unlike关键词匹配失败"""
        keyword_row = {
            'converted_unlike_keyword': '[0, "传统"]',
            'unlike_keyword': '传统'
        }
        input_row = {'company_profile': ['传统燃油车制造']}
        text_columns = ['company_profile']
        
        result = self.engine._match_unlike_keywords(keyword_row, input_row, text_columns)
        self.assertFalse(result.success)
        self.assertIn("Unlike", result.failure_reason)
    
    def test_full_keyword_matching(self):
        """测试完整关键词匹配流程"""
        keyword_row = {
            'converted_like_keyword': '[0, "新能源"]',
            'like_keyword': '新能源',
            'converted_must_keyword': '[0, "汽车"]',
            'must_keyword': '汽车',
            'converted_unlike_keyword': '0',
            'unlike_keyword': ''
        }
        input_row = {
            'company_profile': ['新能源汽车制造企业'],
            'main_product': ['电动汽车']
        }
        text_columns = ['company_profile', 'main_product']
        
        result = self.engine.match_keywords(keyword_row, input_row, text_columns)
        self.assertTrue(result.success)
        self.assertIsNotNone(result.matched_texts)


class TestResultProcessor(unittest.TestCase):
    """结果处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = ResultProcessor()
    
    def test_format_matched_texts_list(self):
        """测试匹配文本列表格式化"""
        matched_texts = ["文本1_like_关键词", "文本2_must_关键词"]
        result = self.processor._format_matched_texts_list(matched_texts)
        
        self.assertIsInstance(result, str)
        self.assertIn("文本1", result)
        self.assertIn("文本2", result)
    
    def test_format_single_result_success(self):
        """测试单个成功结果格式化"""
        result_data = {
            'keyword_index': 0,
            'company_id': 'C001',
            'company_name': '测试公司',
            'match_success': True,
            'matched_texts': {
                'company_profile': ['匹配文本1'],
                'main_product': ['匹配文本2']
            }
        }
        
        formatted = self.processor._format_single_result(result_data)
        self.assertIsNotNone(formatted)
        self.assertEqual(formatted['keyword_index'], 0)
        self.assertEqual(formatted['company_id'], 'C001')
    
    def test_format_single_result_failure(self):
        """测试单个失败结果格式化"""
        result_data = {
            'keyword_index': 0,
            'company_id': 'C001',
            'match_success': False
        }
        
        formatted = self.processor._format_single_result(result_data)
        self.assertIsNone(formatted)
    
    def test_generate_summary_report(self):
        """测试摘要报告生成"""
        matching_results = [
            {'match_success': True, 'matched_texts': {'company_profile': ['文本1']}},
            {'match_success': False, 'failure_reason': '测试失败'},
            {'match_success': True, 'matched_texts': {'main_product': ['文本2']}}
        ]
        
        report = self.processor.generate_summary_report(matching_results)
        
        self.assertEqual(report['total_processed'], 3)
        self.assertEqual(report['successful_matches'], 2)
        self.assertEqual(report['failed_matches'], 1)
        self.assertAlmostEqual(report['match_rate'], 2/3, places=2)


class TestKeywordMatcher(unittest.TestCase):
    """关键词匹配器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.matcher = KeywordMatcher()
    
    @patch('pandas.read_excel')
    def test_load_keyword_data(self, mock_read_excel):
        """测试关键词数据加载"""
        # 模拟Excel数据
        mock_df = pd.DataFrame({
            'chain_name': ['产业链1'],
            'converted_like_keyword': ['[0, "关键词"]']
        })
        mock_read_excel.return_value = mock_df
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            result = self.matcher.load_keyword_data(tmp_path)
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 1)
        finally:
            Path(tmp_path).unlink(missing_ok=True)
    
    @patch('pandas.read_excel')
    def test_load_input_data_excel(self, mock_read_excel):
        """测试输入数据加载（Excel）"""
        mock_df = pd.DataFrame({
            'company_id': ['C001'],
            'company_name': ['测试公司'],
            'company_profile': [['公司简介']]
        })
        mock_read_excel.return_value = mock_df
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            result = self.matcher.load_input_data(tmp_path)
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 1)
        finally:
            Path(tmp_path).unlink(missing_ok=True)
    
    @patch('pandas.read_csv')
    def test_load_input_data_csv(self, mock_read_csv):
        """测试输入数据加载（CSV）"""
        mock_df = pd.DataFrame({
            'company_id': ['C001'],
            'company_name': ['测试公司']
        })
        mock_read_csv.return_value = mock_df
        
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            result = self.matcher.load_input_data(tmp_path)
            self.assertIsInstance(result, pd.DataFrame)
            self.assertEqual(len(result), 1)
        finally:
            Path(tmp_path).unlink(missing_ok=True)
    
    def test_auto_detect_keyword_file(self):
        """测试自动检测关键词文件"""
        # 创建测试文件
        test_file = Path('converted_keyword_rules_test.xlsx')
        test_file.touch()
        
        try:
            result = self.matcher._auto_detect_keyword_file()
            self.assertIsNotNone(result)
            self.assertIn('converted_keyword_rules', result)
        finally:
            test_file.unlink(missing_ok=True)


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("关键词匹配功能单元测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestKeywordCompiler,
        TestFilterLogic,
        TestMatchingEngine,
        TestResultProcessor,
        TestKeywordMatcher
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
