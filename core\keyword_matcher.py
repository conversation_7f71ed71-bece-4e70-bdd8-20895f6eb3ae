#!/usr/bin/env python3
"""
关键词匹配器主控制模块
Keyword Matcher Main Controller Module

该模块是智能关键词匹配功能的主控制器，协调各个子模块完成整体匹配流程。
实现choice=4的核心功能，提供完整的关键词匹配和标记服务。

主要功能：
1. 读取关键词表格和输入数据表格
2. 协调筛选逻辑、匹配引擎和结果处理
3. 提供批量处理和性能优化
4. 生成详细的匹配报告

作者：系统开发
日期：2024年
"""

import pandas as pd
import logging
from typing import List, Dict, Set, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import sys
import os
import random
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import config_manager


def process_combination_batch_worker(combination_batch: List[Tuple]) -> List[Dict[str, Any]]:
    """
    独立的批处理工作函数 - 用于进程池处理

    Args:
        combination_batch: 包含(keyword_idx, keyword_row_dict, input_row_dict)元组的列表

    Returns:
        List[Dict[str, Any]]: 匹配结果列表
    """
    batch_results = []

    # 在工作进程中重新初始化组件
    from core.filter_logic import FilterLogic
    from core.matching_engine import MatchingEngine
    from config import config_manager

    filter_logic = FilterLogic()
    matching_engine = MatchingEngine()

    # 获取标识列配置
    identifier_columns = config_manager.get_list('keyword_matching.input_table_columns.identifier_columns')

    for keyword_idx, keyword_row_dict, input_row_dict in combination_batch:
        try:
            # 应用筛选逻辑
            filter_result = filter_logic.apply_combined_filter(keyword_row_dict, input_row_dict)
            if not filter_result.passed:
                continue

            # 执行关键词匹配
            match_result = matching_engine.match_keywords(
                keyword_row_dict, input_row_dict, filter_result.filtered_columns
            )

            if match_result.success:
                # 构建结果字典
                result = {
                    'keyword_index': keyword_idx,
                    'keyword_rule_id': keyword_row_dict.get('id', keyword_idx),
                    'keyword_rule_name': keyword_row_dict.get('name', f'规则{keyword_idx}'),
                    'match_success': True,
                    'matched_texts': match_result.matched_texts,
                    'match_details': match_result.match_details,  # 添加完整的match_details
                    'total_texts_processed': match_result.match_details.get('total_texts_processed', 0),
                    'texts_passed_all_stages': match_result.match_details.get('texts_passed_all_stages', 0),
                    'like_passed_count': match_result.match_details.get('stage_stats', {}).get('like_passed', 0),
                    'must_passed_count': match_result.match_details.get('stage_stats', {}).get('must_passed', 0),
                    'unlike_passed_count': match_result.match_details.get('stage_stats', {}).get('unlike_passed', 0),
                    'like_failed_count': match_result.match_details.get('stage_stats', {}).get('like_failed', 0),
                    'must_failed_count': match_result.match_details.get('stage_stats', {}).get('must_failed', 0),
                    'unlike_failed_count': match_result.match_details.get('stage_stats', {}).get('unlike_failed', 0)
                }

                # 添加输入数据的标识列
                for col in identifier_columns:
                    if col in input_row_dict:
                        result[col] = input_row_dict[col]

                # 添加配置化的标识字段（用于过滤验证）
                # 从配置获取字段名称
                keyword_identifier_field = config_manager.get_str('keyword_matching.result_fields.keyword_identifier.field_name', 'keyword_index')
                company_identifier_field = config_manager.get_str('keyword_matching.result_fields.company_identifier.field_name', 'company_id')

                # 设置配置化字段
                result[keyword_identifier_field] = keyword_idx
                result[company_identifier_field] = input_row_dict.get('lc_company_id', '')

                batch_results.append(result)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理组合失败 (keyword_idx={keyword_idx}): {e}")

    return batch_results
from core.filter_logic import FilterLogic
from core.matching_engine import MatchingEngine
from core.result_processor import ResultProcessor
from utils.progress_bar import create_progress_bar, ProgressBar


@dataclass
class MatchingProgress:
    """匹配进度数据类"""
    total_combinations: int = 0
    processed_combinations: int = 0
    successful_matches: int = 0
    failed_matches: int = 0
    current_input_row: int = 0
    total_input_rows: int = 0


class KeywordMatcher:
    """
    关键词匹配器主控制类
    
    协调各个子模块完成智能关键词匹配的完整流程，
    提供高效的批量处理和详细的进度跟踪。
    """
    
    def __init__(self):
        """初始化关键词匹配器"""
        self.logger = logging.getLogger(__name__)

        # 初始化子模块
        self.filter_logic = FilterLogic()
        self.matching_engine = MatchingEngine()
        self.result_processor = ResultProcessor()

        # 从配置获取设置
        self.batch_size = config_manager.get_int('keyword_matching.batch_size', 1000)
        self.max_workers = config_manager.get_int('keyword_matching.max_workers', 4)
        self.enable_optimization = config_manager.get_bool('keyword_matching.enable_performance_optimization', True)

        # 并行处理方式配置：'thread' 或 'process'
        self.parallel_mode = config_manager.get_str('keyword_matching.parallel_mode', 'process')

        # 获取文件路径配置
        self.keyword_file_path = config_manager.get_str('keyword_matching.keyword_file_path')
        self.keyword_sheet_name = config_manager.get_str('keyword_matching.keyword_sheet_name')
        self.input_file_path = config_manager.get_str('keyword_matching.input_file_path')
        self.input_sheet_name = config_manager.get_str('keyword_matching.input_sheet_name')

        # 从配置获取结果字段名称
        self.keyword_identifier_field = config_manager.get_str('keyword_matching.result_fields.keyword_identifier.field_name', 'keyword_index')
        self.company_identifier_field = config_manager.get_str('keyword_matching.result_fields.company_identifier.field_name', 'company_id')

        # 获取parquet数据源配置
        self.parquet_config = config_manager.get_dict('keyword_matching.parquet_data_source', {})
        self.parquet_enabled = self.parquet_config.get('enable', False)
        self.folder_path_1 = self.parquet_config.get('folder_path_1', '')
        self.folder_path_2 = self.parquet_config.get('folder_path_2', '')
        self.merge_columns = self.parquet_config.get('merge_on_columns', ['lc_company_id', 'company_name'])

        # 验证parquet配置
        if self.parquet_enabled:
            self._validate_parquet_config()

        # 进度跟踪
        self.progress = MatchingProgress()
        self.progress_bar: Optional[ProgressBar] = None

        self.logger.info("关键词匹配器初始化完成")

    def _validate_parquet_config(self):
        """验证parquet配置的有效性"""
        errors = []
        warnings = []

        # 检查文件夹路径
        if not self.folder_path_1:
            errors.append("Parquet文件夹路径1未配置")
        elif not Path(self.folder_path_1).exists():
            warnings.append(f"Parquet文件夹1不存在: {self.folder_path_1}")

        if not self.folder_path_2:
            errors.append("Parquet文件夹路径2未配置")
        elif not Path(self.folder_path_2).exists():
            warnings.append(f"Parquet文件夹2不存在: {self.folder_path_2}")

        # 检查合并列配置
        if not self.merge_columns:
            errors.append("合并列配置为空")
        elif not isinstance(self.merge_columns, list):
            errors.append("合并列配置必须是列表格式")

        # 记录验证结果
        if errors:
            error_msg = "Parquet配置验证失败: " + "; ".join(errors)
            self.logger.error(error_msg)
            # 不抛出异常，只是禁用parquet功能
            self.parquet_enabled = False
            self.logger.warning("已禁用Parquet数据源功能")

        if warnings:
            for warning in warnings:
                self.logger.warning(f"Parquet配置警告: {warning}")

        if self.parquet_enabled and not errors:
            self.logger.info("Parquet配置验证通过")

    def _calculate_progress_update_interval(self) -> float:
        """
        根据数据量动态计算进度条更新间隔

        Returns:
            float: 更新间隔（秒）
        """
        total_combinations = self.progress.total_combinations

        if total_combinations <= 10000:
            # 小数据量：更频繁更新
            return 0.1
        elif total_combinations <= 100000:
            # 中等数据量：标准更新
            return 0.5
        elif total_combinations <= 1000000:
            # 大数据量：较少更新
            return 1.0
        else:
            # 超大数据量：最少更新
            return 2.0

    def _get_progress_update_frequency(self) -> int:
        """
        获取进度条更新频率（每多少次处理更新一次）

        Returns:
            int: 更新频率
        """
        total_combinations = self.progress.total_combinations

        if total_combinations <= 1000:
            # 小数据量：每次都更新
            return 1
        elif total_combinations <= 10000:
            # 小中数据量：每10次更新
            return 10
        elif total_combinations <= 100000:
            # 中等数据量：每100次更新
            return 100
        elif total_combinations <= 1000000:
            # 大数据量：每1000次更新
            return 1000
        else:
            # 超大数据量：每5000次更新
            return 5000
    
    def load_keyword_data(self, file_path: Optional[str] = None, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        加载关键词数据表格
        
        Args:
            file_path: 关键词文件路径（可选）
            sheet_name: 工作表名称（可选）
            
        Returns:
            pd.DataFrame: 关键词数据表格
        """
        # 使用参数或配置中的路径
        file_path = file_path or self.keyword_file_path
        sheet_name = sheet_name or self.keyword_sheet_name
        
        # 自动检测文件路径
        if file_path == "auto_detect":
            file_path = self._auto_detect_keyword_file()
        
        if not file_path or not Path(file_path).exists():
            raise FileNotFoundError(f"关键词文件不存在: {file_path}")
        
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            self.logger.info(f"成功加载关键词数据: {len(df)} 行, 文件: {file_path}")
            return df
        except Exception as e:
            self.logger.error(f"加载关键词数据失败: {e}")
            raise
    
    def load_input_data(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        加载输入数据表格

        Args:
            file_path: 输入文件路径或parquet文件名
            sheet_name: 工作表名称（可选）

        Returns:
            pd.DataFrame: 输入数据表格
        """
        sheet_name = sheet_name or self.input_sheet_name

        # 检查是否为parquet文件名（不含路径分隔符）
        if self.parquet_enabled and '/' not in file_path and '\\' not in file_path and file_path.endswith('.parquet'):
            # 使用parquet文件夹模式
            return self.load_parquet_data_from_folders(file_path)

        # 传统文件路径模式
        if not Path(file_path).exists():
            raise FileNotFoundError(f"输入文件不存在: {file_path}")

        try:
            # 根据文件扩展名选择读取方法
            file_ext = Path(file_path).suffix.lower()
            if file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            elif file_ext == '.csv':
                df = pd.read_csv(file_path)
            elif file_ext == '.parquet':
                df = pd.read_parquet(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            self.logger.info(f"成功加载输入数据: {len(df)} 行, 文件: {file_path}")
            return df
        except Exception as e:
            self.logger.error(f"加载输入数据失败: {e}")
            raise

    def load_parquet_data_from_folders(self, file_name: str) -> pd.DataFrame:
        """
        从两个parquet文件夹中读取同名文件并合并

        Args:
            file_name: parquet文件名（不含路径）

        Returns:
            pd.DataFrame: 合并后的数据表格
        """
        if not self.parquet_enabled:
            raise ValueError("Parquet数据源未启用，请检查配置")

        if not self.folder_path_1 or not self.folder_path_2:
            raise ValueError("Parquet文件夹路径未配置")

        # 构建完整文件路径
        file_path_1 = Path(self.folder_path_1) / file_name
        file_path_2 = Path(self.folder_path_2) / file_name

        # 检查文件是否存在
        if not file_path_1.exists():
            raise FileNotFoundError(f"文件不存在: {file_path_1}")
        if not file_path_2.exists():
            raise FileNotFoundError(f"文件不存在: {file_path_2}")

        try:
            # 读取两个parquet文件
            self.logger.info(f"读取parquet文件: {file_path_1}")
            df1 = pd.read_parquet(file_path_1)

            self.logger.info(f"读取parquet文件: {file_path_2}")
            df2 = pd.read_parquet(file_path_2)

            # 验证合并列是否存在
            for col in self.merge_columns:
                if col not in df1.columns:
                    raise ValueError(f"合并列 '{col}' 在文件1中不存在: {file_path_1}")
                if col not in df2.columns:
                    raise ValueError(f"合并列 '{col}' 在文件2中不存在: {file_path_2}")

            # 合并数据
            self.logger.info(f"合并数据，基于列: {self.merge_columns}")
            merged_df = pd.merge(df1, df2, on=self.merge_columns, how='inner')

            self.logger.info(f"成功合并parquet数据: 文件1 {len(df1)} 行, 文件2 {len(df2)} 行, 合并后 {len(merged_df)} 行")
            return merged_df

        except Exception as e:
            self.logger.error(f"加载和合并parquet数据失败: {e}")
            raise

    def get_available_parquet_files(self) -> List[str]:
        """
        获取两个文件夹中的同名parquet文件列表

        Returns:
            List[str]: 同名parquet文件名列表
        """
        if not self.parquet_enabled:
            return []

        if not self.folder_path_1 or not self.folder_path_2:
            return []

        try:
            folder1 = Path(self.folder_path_1)
            folder2 = Path(self.folder_path_2)

            if not folder1.exists() or not folder2.exists():
                self.logger.warning(f"Parquet文件夹不存在: {folder1} 或 {folder2}")
                return []

            # 获取两个文件夹中的parquet文件
            files1 = set(f.name for f in folder1.glob('*.parquet'))
            files2 = set(f.name for f in folder2.glob('*.parquet'))

            # 返回同名文件
            common_files = list(files1.intersection(files2))
            common_files.sort()

            self.logger.info(f"找到 {len(common_files)} 个同名parquet文件")
            return common_files

        except Exception as e:
            self.logger.error(f"获取parquet文件列表失败: {e}")
            return []

    def get_random_parquet_file(self) -> Optional[str]:
        """
        随机选择一个parquet文件用于测试

        Returns:
            Optional[str]: 随机选择的文件名，如果没有文件则返回None
        """
        available_files = self.get_available_parquet_files()
        if not available_files:
            return None

        selected_file = random.choice(available_files)
        self.logger.info(f"随机选择测试文件: {selected_file}")
        return selected_file

    def run_batch_processing(self, keyword_file_path: Optional[str] = None,
                           output_base_path: Optional[str] = None) -> List[Tuple[str, str, str]]:
        """
        批量处理所有parquet文件

        Args:
            keyword_file_path: 关键词文件路径（可选）
            output_base_path: 输出文件基础路径（可选）

        Returns:
            List[Tuple[str, str, str]]: (文件名, 结果文件路径, 报告文件路径) 的列表
        """
        if not self.parquet_enabled:
            raise ValueError("Parquet数据源未启用，无法进行批处理")

        available_files = self.get_available_parquet_files()
        if not available_files:
            raise ValueError("没有找到可处理的parquet文件")

        batch_config = self.parquet_config.get('batch_processing', {})
        continue_on_error = batch_config.get('continue_on_error', True)
        max_errors = batch_config.get('max_errors', 50)
        progress_interval = batch_config.get('progress_report_interval', 10)

        results = []
        error_count = 0

        self.logger.info(f"开始批量处理 {len(available_files)} 个parquet文件")

        for i, file_name in enumerate(available_files, 1):
            try:
                self.logger.info(f"处理文件 {i}/{len(available_files)}: {file_name}")

                # 生成输出文件路径
                base_name = Path(file_name).stem
                if output_base_path:
                    output_file = f"{output_base_path}_{base_name}.xlsx"
                else:
                    output_file = f"batch_results_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

                # 执行单个文件的匹配
                result_file, report_file = self.run_complete_matching(
                    input_file_path=file_name,  # 传递文件名，load_input_data会识别为parquet模式
                    keyword_file_path=keyword_file_path,
                    output_file_path=output_file
                )

                results.append((file_name, result_file, report_file))

                # 定期报告进度
                if i % progress_interval == 0:
                    self.logger.info(f"批处理进度: {i}/{len(available_files)} 完成")

            except Exception as e:
                error_count += 1
                self.logger.error(f"处理文件 {file_name} 失败: {e}")

                if not continue_on_error or error_count >= max_errors:
                    self.logger.error(f"错误过多或设置不继续处理，停止批处理")
                    break

        self.logger.info(f"批处理完成: 成功 {len(results)} 个文件, 失败 {error_count} 个文件")
        return results

    def run_random_test(self, keyword_file_path: Optional[str] = None,
                       output_file_path: Optional[str] = None) -> Tuple[str, str, str]:
        """
        随机选择一个文件进行快速测试

        Args:
            keyword_file_path: 关键词文件路径（可选）
            output_file_path: 输出文件路径（可选）

        Returns:
            Tuple[str, str, str]: (测试文件名, 结果文件路径, 报告文件路径)
        """
        if not self.parquet_enabled:
            raise ValueError("Parquet数据源未启用，无法进行随机测试")

        test_file = self.get_random_parquet_file()
        if not test_file:
            raise ValueError("没有找到可测试的parquet文件")

        # 生成输出文件路径
        if not output_file_path:
            base_name = Path(test_file).stem
            output_file_path = f"random_test_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        self.logger.info(f"开始随机测试，文件: {test_file}")

        # 执行匹配
        result_file, report_file = self.run_complete_matching(
            input_file_path=test_file,  # 传递文件名，load_input_data会识别为parquet模式
            keyword_file_path=keyword_file_path,
            output_file_path=output_file_path
        )

        self.logger.info(f"随机测试完成: {test_file}")
        return test_file, result_file, report_file

    def execute_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        执行关键词匹配
        
        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格
            
        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        start_time = datetime.now()
        
        # 初始化进度
        self.progress.total_input_rows = len(input_df)
        self.progress.total_combinations = len(keyword_df) * len(input_df)
        self.progress.processed_combinations = 0
        self.progress.successful_matches = 0
        self.progress.failed_matches = 0
        
        self.logger.info(f"开始执行匹配: {len(keyword_df)} 个关键词规则 × {len(input_df)} 条输入数据")

        # 启动进度条（根据数据量动态调整更新间隔）
        update_interval = self._calculate_progress_update_interval()
        self.progress_bar = create_progress_bar(
            total=self.progress.total_combinations,
            description="关键词匹配",
            bar_length=20,
            update_interval=update_interval
        )
        self.progress_bar.start()

        matching_results = []
        
        try:
            if self.enable_optimization and self.max_workers > 1:
                # 并行处理
                matching_results = self._execute_parallel_matching(keyword_df, input_df)
            else:
                # 串行处理
                matching_results = self._execute_serial_matching(keyword_df, input_df)
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # 完成进度条显示
            if self.progress_bar:
                self.progress_bar.finish(f"匹配完成! 成功: {self.progress.successful_matches}, 失败: {self.progress.failed_matches}")
                self.progress_bar = None

            self.logger.info(f"匹配完成: 处理时间 {processing_time:.2f}s, "
                           f"成功匹配 {self.progress.successful_matches} 条, "
                           f"失败 {self.progress.failed_matches} 条")

            return matching_results
            
        except Exception as e:
            # 确保进度条在异常时也能正确关闭
            if self.progress_bar:
                self.progress_bar.finish("匹配过程中发生错误")
                self.progress_bar = None
            self.logger.error(f"执行匹配时发生错误: {e}")
            raise
    
    def _execute_serial_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """串行执行匹配"""
        matching_results = []
        
        for input_idx, input_row in input_df.iterrows():
            self.progress.current_input_row = input_idx + 1
            
            for keyword_idx, keyword_row in keyword_df.iterrows():
                result = self._process_single_combination(keyword_idx, keyword_row, input_row)
                if result:
                    matching_results.append(result)
                
                self.progress.processed_combinations += 1

                # 根据数据量动态调整进度条更新频率
                update_frequency = self._get_progress_update_frequency()
                if self.progress_bar and self.progress.processed_combinations % update_frequency == 0:
                    self.progress_bar.update(self.progress.processed_combinations)

                # 定期输出进度日志（降低频率以避免影响性能）
                if self.progress.processed_combinations % (update_frequency * 10) == 0:
                    self._log_progress()
        
        return matching_results
    
    def _execute_parallel_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        并行执行匹配 - 改进版本

        按组合数分批而不是按输入行分批，确保充分利用并行处理能力
        """
        matching_results = []

        # 生成所有组合
        total_combinations = len(keyword_df) * len(input_df)

        # 智能选择并行模式和批处理大小
        optimal_parallel_mode, optimal_batch_size = self._calculate_optimal_parallel_config(total_combinations)
        actual_batch_size = min(self.batch_size, optimal_batch_size)

        # 如果智能模式与配置不同，给出建议
        if optimal_parallel_mode != self.parallel_mode:
            self.logger.info(f"建议并行模式: {optimal_parallel_mode} (当前: {self.parallel_mode})")

        self.logger.info(f"总组合数: {total_combinations}, 配置批处理大小: {self.batch_size}, 优化批处理大小: {actual_batch_size}, 工作进程/线程: {self.max_workers}")

        # 创建组合批次
        combination_batches = []
        batch_combinations = []

        for input_idx, input_row in input_df.iterrows():
            for keyword_idx, keyword_row in keyword_df.iterrows():
                # 转换为字典格式以避免序列化问题
                keyword_row_dict = keyword_row.to_dict()
                input_row_dict = input_row.to_dict()
                batch_combinations.append((keyword_idx, keyword_row_dict, input_row_dict))

                # 当批次达到优化大小时，创建新批次
                if len(batch_combinations) >= actual_batch_size:
                    combination_batches.append(batch_combinations)
                    batch_combinations = []

        # 添加剩余的组合
        if batch_combinations:
            combination_batches.append(batch_combinations)

        self.logger.info(f"创建了 {len(combination_batches)} 个批次进行并行处理")

        # 选择执行器类型
        executor_class = ProcessPoolExecutor if self.parallel_mode == 'process' else ThreadPoolExecutor

        with executor_class(max_workers=self.max_workers) as executor:
            # 提交批处理任务
            if self.parallel_mode == 'process':
                # 进程池使用独立的工作函数
                future_to_batch = {
                    executor.submit(process_combination_batch_worker, batch): batch_idx
                    for batch_idx, batch in enumerate(combination_batches)
                }
            else:
                # 线程池使用实例方法
                future_to_batch = {
                    executor.submit(self._process_combination_batch, batch, batch_idx): batch_idx
                    for batch_idx, batch in enumerate(combination_batches)
                }

            # 收集结果并更新进度
            completed_batches = 0
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_results = future.result()
                    matching_results.extend(batch_results)
                    completed_batches += 1

                    # 更新进度统计
                    batch_size_actual = len(combination_batches[batch_idx])
                    self.progress.processed_combinations += batch_size_actual
                    self.progress.successful_matches += len(batch_results)
                    self.progress.failed_matches += batch_size_actual - len(batch_results)

                    # 更新进度条
                    if self.progress_bar:
                        self.progress_bar.update(self.progress.processed_combinations)

                    self.logger.debug(f"批次 {batch_idx} 处理完成，结果数: {len(batch_results)} ({completed_batches}/{len(combination_batches)})")

                except Exception as e:
                    self.logger.error(f"批次 {batch_idx} 处理失败: {e}")
                    completed_batches += 1

        return matching_results

    def _calculate_optimal_parallel_config(self, total_combinations: int) -> tuple:
        """
        根据数据规模智能选择最优并行配置

        Args:
            total_combinations: 总组合数

        Returns:
            tuple: (推荐并行模式, 推荐批处理大小)
        """
        if not self.enable_optimization or self.max_workers <= 1:
            return 'serial', total_combinations

        # 根据数据规模选择最优策略
        if total_combinations < 1000:
            # 小数据量：串行处理最快
            return 'serial', total_combinations
        elif total_combinations < 10000:
            # 中等数据量：线程池并行
            optimal_batch_size = max(50, total_combinations // (self.max_workers * 3))
            return 'thread', min(200, optimal_batch_size)
        else:
            # 大数据量：进程池并行，能显著提升CPU占用
            optimal_batch_size = max(200, total_combinations // (self.max_workers * 2))
            return 'process', min(1000, optimal_batch_size)

    def _calculate_optimal_batch_size(self, total_combinations: int) -> int:
        """
        计算最优批处理大小

        Args:
            total_combinations: 总组合数

        Returns:
            int: 最优批处理大小
        """
        if not self.enable_optimization or self.max_workers <= 1:
            return total_combinations  # 串行处理时使用全部数据

        # 根据并行模式调整策略
        if self.parallel_mode == 'process':
            # 进程池：确保每个进程有足够的工作量，减少进程间通信开销
            min_batches_per_worker = 2
            target_total_batches = self.max_workers * min_batches_per_worker

            # 进程池使用较大的批处理大小
            optimal_size = max(100, total_combinations // target_total_batches)
            min_batch_size = 50
            max_batch_size = 1000
        else:
            # 线程池：使用较小的批处理大小，提高响应性
            min_batches_per_worker = 4
            target_total_batches = self.max_workers * min_batches_per_worker

            optimal_size = max(25, total_combinations // target_total_batches)
            min_batch_size = 10
            max_batch_size = 200

        optimal_size = max(min_batch_size, min(max_batch_size, optimal_size))

        self.logger.info(f"批处理优化: 模式={self.parallel_mode}, 总组合={total_combinations}, 目标批次数={target_total_batches}, 优化大小={optimal_size}")

        return optimal_size

    def _process_combination_batch(self, combination_batch: List[Tuple], batch_idx: int) -> List[Dict[str, Any]]:
        """
        处理组合批次 - 新的并行处理方法

        Args:
            combination_batch: 包含(keyword_idx, keyword_row, input_row)元组的列表
            batch_idx: 批次索引

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        batch_results = []

        # 在子进程/线程中重新初始化组件（避免序列化问题）
        import logging
        logger = logging.getLogger(__name__)

        # 重新初始化子模块
        from core.filter_logic import FilterLogic
        from core.matching_engine import MatchingEngine
        from config import config_manager

        filter_logic = FilterLogic()
        matching_engine = MatchingEngine()

        for keyword_idx, keyword_row_dict, input_row_dict in combination_batch:
            try:
                # 数据已经是字典格式，直接使用
                keyword_dict = keyword_row_dict
                input_dict = input_row_dict

                # 应用筛选逻辑
                filter_result = filter_logic.apply_combined_filter(keyword_dict, input_dict)
                if not filter_result.passed:
                    continue

                # 执行关键词匹配
                match_result = matching_engine.match_keywords(
                    keyword_dict, input_dict, filter_result.filtered_columns
                )

                if match_result.success:
                    # 构建结果字典
                    result = {
                        'keyword_index': keyword_idx,
                        'keyword_rule_id': keyword_dict.get('id', keyword_idx),
                        'keyword_rule_name': keyword_dict.get('name', f'规则{keyword_idx}'),
                        'match_success': True,
                        'matched_texts': match_result.matched_texts,
                        'match_details': match_result.match_details,  # 添加完整的match_details
                        'total_texts_processed': match_result.match_details.get('total_texts_processed', 0),
                        'texts_passed_all_stages': match_result.match_details.get('texts_passed_all_stages', 0),
                        'like_passed_count': match_result.match_details.get('stage_stats', {}).get('like_passed', 0),
                        'must_passed_count': match_result.match_details.get('stage_stats', {}).get('must_passed', 0),
                        'unlike_passed_count': match_result.match_details.get('stage_stats', {}).get('unlike_passed', 0),
                        'like_failed_count': match_result.match_details.get('stage_stats', {}).get('like_failed', 0),
                        'must_failed_count': match_result.match_details.get('stage_stats', {}).get('must_failed', 0),
                        'unlike_failed_count': match_result.match_details.get('stage_stats', {}).get('unlike_failed', 0)
                    }

                    # 添加输入数据的标识列
                    identifier_columns = config_manager.get_list('keyword_matching.input_table_columns.identifier_columns')
                    for col in identifier_columns:
                        if col in input_dict:
                            result[col] = input_dict[col]

                    # 添加配置化的标识字段（用于过滤验证）
                    result[self.keyword_identifier_field] = keyword_idx
                    result[self.company_identifier_field] = input_dict.get('lc_company_id', '')

                    batch_results.append(result)

            except Exception as e:
                logger.error(f"处理组合失败 (keyword_idx={keyword_idx}): {e}")

        return batch_results
    
    def _process_single_combination(self, keyword_idx: int, keyword_row: pd.Series, input_row: pd.Series) -> Optional[Dict[str, Any]]:
        """
        处理单个关键词-输入数据组合

        Args:
            keyword_idx: 关键词索引
            keyword_row: 关键词行数据
            input_row: 输入行数据

        Returns:
            Dict[str, Any]: 匹配结果，只有匹配成功时才返回，失败时返回None
        """
        try:
            # 转换为字典格式
            keyword_dict = keyword_row.to_dict()
            input_dict = input_row.to_dict()

            # 应用筛选逻辑
            filter_result = self.filter_logic.apply_combined_filter(keyword_dict, input_dict)
            if not filter_result.passed:
                self.progress.failed_matches += 1
                return None

            # 执行关键词匹配
            match_result = self.matching_engine.match_keywords(
                keyword_dict, input_dict, filter_result.filtered_columns
            )

            # 只有匹配成功的记录才构建结果并返回
            if match_result.success:
                # 提取match_details中的关键统计信息
                match_details = match_result.match_details or {}
                stage_stats = match_details.get('stage_stats', {})

                # 构建包含展开统计信息的匹配结果
                result = {
                    # 标识信息（使用配置化字段名称）
                    self.keyword_identifier_field: keyword_idx,  # 关键词规则标识
                    self.company_identifier_field: input_dict.get('lc_company_id', ''),  # 企业标识（修复字段名）

                    # 添加所有标识列
                    'lc_company_id': input_dict.get('lc_company_id', ''),
                    'company_name': input_dict.get('company_name', ''),

                    # 匹配结果信息
                    'match_success': True,
                    'matched_texts': match_result.matched_texts,  # 具体匹配到的文本内容和标记
                    'match_details': match_details,  # 保留完整的详细匹配统计信息

                    # 展开的统计信息字段
                    'total_texts_processed': match_details.get('total_texts_processed', 0),
                    'texts_passed_all_stages': match_details.get('texts_passed_all_stages', 0),
                    'like_passed_count': stage_stats.get('like_passed', 0),
                    'must_passed_count': stage_stats.get('must_passed', 0),
                    'unlike_passed_count': stage_stats.get('unlike_passed', 0),
                    'like_failed_count': stage_stats.get('like_failed', 0),
                    'must_failed_count': stage_stats.get('must_failed', 0),
                    'unlike_failed_count': stage_stats.get('unlike_failed', 0),

                    # 筛选信息
                    'filter_reason': filter_result.reason,
                    'filtered_columns': filter_result.filtered_columns,

                    # 处理时间戳
                    'processed_at': datetime.now().isoformat()
                }

                self.progress.successful_matches += 1
                return result
            else:
                # 匹配失败的记录不保留在最终结果中
                self.progress.failed_matches += 1
                return None

        except Exception as e:
            self.logger.warning(f"处理组合失败 (keyword_idx={keyword_idx}): {e}")
            self.progress.failed_matches += 1
            return None
    
    def _auto_detect_keyword_file(self) -> Optional[str]:
        """自动检测关键词文件"""
        # 查找最新的转换后关键词文件
        current_dir = Path('.')
        pattern = 'converted_keyword_rules*.xlsx'
        
        files = list(current_dir.glob(pattern))
        if files:
            # 按修改时间排序，返回最新的
            latest_file = max(files, key=lambda f: f.stat().st_mtime)
            return str(latest_file)
        
        return None
    
    def _log_progress(self):
        """输出进度信息"""
        if self.progress.total_combinations > 0:
            progress_pct = (self.progress.processed_combinations / self.progress.total_combinations) * 100
            self.logger.info(f"匹配进度: {progress_pct:.1f}% "
                           f"({self.progress.processed_combinations}/{self.progress.total_combinations}), "
                           f"成功: {self.progress.successful_matches}, "
                           f"失败: {self.progress.failed_matches}")
    
    def run_complete_matching(self, input_file_path: str,
                            keyword_file_path: Optional[str] = None,
                            output_file_path: Optional[str] = None) -> Tuple[str, str]:
        """
        运行完整的匹配流程

        Args:
            input_file_path: 输入文件路径
            keyword_file_path: 关键词文件路径（可选）
            output_file_path: 输出文件路径（可选）

        Returns:
            Tuple[str, str]: (结果文件路径, 摘要报告路径)
        """
        try:
            # 加载数据
            self.logger.info("开始加载数据...")
            keyword_df = self.load_keyword_data(keyword_file_path)
            input_df = self.load_input_data(input_file_path)

            # 执行匹配
            self.logger.info("开始执行匹配...")
            matching_results = self.execute_matching(keyword_df, input_df)

            # 过滤和验证匹配结果
            self.logger.info("过滤和验证匹配结果...")
            successful_results = self._filter_successful_results(matching_results)

            # 记录结果统计
            total_results = len(matching_results) if matching_results else 0
            successful_count = len(successful_results)
            self.logger.info(f"匹配结果统计: 总处理 {total_results} 条组合, "
                           f"成功匹配 {successful_count} 条, "
                           f"成功率: {(successful_count/total_results*100):.2f}%" if total_results > 0 else "成功率: 0%")

            # 处理和保存结果
            self.logger.info("开始处理和保存结果...")
            result_file_path = self.result_processor.process_and_save_results(
                successful_results,
                output_file_path
            )

            # 生成摘要报告
            self.logger.info("生成摘要报告...")
            summary_report = self.result_processor.generate_summary_report(successful_results)
            report_file_path = self.result_processor.save_summary_report(summary_report)

            self.logger.info(f"完整匹配流程执行完成")
            self.logger.info(f"结果文件: {result_file_path}")
            self.logger.info(f"摘要报告: {report_file_path}")

            return result_file_path, report_file_path

        except Exception as e:
            self.logger.error(f"完整匹配流程执行失败: {e}")
            raise

    def _filter_successful_results(self, matching_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤出成功匹配的结果，确保数据完整性

        Args:
            matching_results: 原始匹配结果列表

        Returns:
            List[Dict[str, Any]]: 过滤后的成功匹配结果列表
        """
        if not matching_results:
            return []

        successful_results = []

        for result in matching_results:
            # 验证结果的完整性
            if (result and
                result.get('match_success') is True and
                result.get('matched_texts') and
                result.get('match_details')):

                # 确保包含必要的标识信息（使用配置化字段名称）
                if (self.keyword_identifier_field in result and
                    self.company_identifier_field in result):

                    successful_results.append(result)
                else:
                    keyword_id = result.get(self.keyword_identifier_field, 'unknown')
                    self.logger.warning(f"匹配结果缺少必要的标识信息: {keyword_id}")
            else:
                # 记录被过滤的原因
                if result:
                    reason = "未知原因"
                    if not result.get('match_success'):
                        reason = "匹配失败"
                    elif not result.get('matched_texts'):
                        reason = "无匹配文本"
                    elif not result.get('match_details'):
                        reason = "缺少匹配详情"

                    keyword_id = result.get(self.keyword_identifier_field, 'unknown')
                    self.logger.debug(f"过滤结果 (keyword_id={keyword_id}): {reason}")

        return successful_results
    
    def get_progress_info(self) -> MatchingProgress:
        """获取当前进度信息"""
        return self.progress


def test_keyword_matcher():
    """测试关键词匹配器"""
    print("=" * 60)
    print("关键词匹配器测试")
    print("=" * 60)

    matcher = KeywordMatcher()

    # 这里需要实际的测试文件
    print("注意：需要提供实际的测试文件进行完整测试")
    print(f"当前配置:")
    print(f"  关键词文件: {matcher.keyword_file_path}")
    print(f"  输入文件: {matcher.input_file_path}")
    print(f"  批处理大小: {matcher.batch_size}")
    print(f"  最大工作线程: {matcher.max_workers}")
    print(f"  性能优化: {matcher.enable_optimization}")


def test_single_combination_processing():
    """测试单个组合处理和结果保存的完整逻辑

    测试范围：
    1. 单个组合匹配测试 - 验证匹配逻辑和字段结构
    2. 结果保存逻辑测试 - 验证完整的数据处理流程
    3. 配置化字段名称验证 - 确认字段名称正确使用
    4. match_details详细信息保存验证
    """
    print("\n" + "=" * 80)
    print("单个组合处理和结果保存测试 - 验证完整数据处理流程")
    print("=" * 80)

    matcher = KeywordMatcher()

    # 创建测试数据 - 包含完整的筛选字段
    keyword_data = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "汽车"]',
        'must_keyword': '汽车',
        'converted_unlike_keyword': '0',  # 设为默认值，避免unlike匹配失败
        'unlike_keyword': '',
        # 行业筛选字段 - 修正匹配逻辑
        'industry_type': 'C36',  # 直接使用具体的行业代码
        'industry_l1_code': '',  # 设为空
        # 数据源范围筛选
        'source_scope': 'main_product,service_intro'  # 指定参与匹配的列
    }

    input_data = {
        'company_id': 'TEST001',
        'company_name': '测试新能源汽车公司',
        'main_product': ['电动汽车', '充电桩'],
        'service_intro': ['新能源技术开发'],
        'industry': '制造业',
        'region': '广东省深圳市',
        # 行业代码字段
        'industry_l1_code': 'C36',  # 汽车制造业
        'industry_type': 'C'  # 制造业
    }

    # 转换为pandas Series
    keyword_row = pd.Series(keyword_data)
    input_row = pd.Series(input_data)
    input_row.name = 0  # 设置行索引

    # 测试处理单个组合
    print("测试数据:")
    print(f"关键词规则: like='{keyword_data['like_keyword']}', must='{keyword_data['must_keyword']}', unlike='{keyword_data['unlike_keyword']}'")
    print(f"输入数据: company='{input_data['company_name']}', products={input_data['main_product']}")

    # 先测试筛选逻辑
    keyword_dict = keyword_row.to_dict()
    input_dict = input_row.to_dict()
    filter_result = matcher.filter_logic.apply_combined_filter(keyword_dict, input_dict)
    print(f"\n筛选结果: passed={filter_result.passed}, reason='{filter_result.reason}'")
    print(f"筛选后的列: {filter_result.filtered_columns}")

    if filter_result.passed:
        # 测试匹配引擎
        match_result = matcher.matching_engine.match_keywords(
            keyword_dict, input_dict, filter_result.filtered_columns
        )
        print(f"匹配引擎结果: success={match_result.success}")
        if match_result.success:
            print(f"匹配文本: {match_result.matched_texts}")
            print(f"匹配详情: {match_result.match_details}")

    # 测试完整的组合处理
    result = matcher._process_single_combination(0, keyword_row, input_row)

    if result:
        print(f"\n✅ 匹配结果: 成功")
        print(f"匹配文本: {result['matched_texts']}")
        print(f"匹配详情keys: {list(result['match_details'].keys())}")
        print(f"包含完整信息:")

        # 使用配置化的字段名称
        keyword_field = matcher.keyword_identifier_field
        company_field = matcher.company_identifier_field

        print(f"  - {keyword_field}: {result[keyword_field]}")
        print(f"  - {company_field}: {result[company_field]}")
        print(f"  - 处理时间: {result['processed_at']}")

        # 验证match_details的完整性
        match_details = result['match_details']
        if 'total_texts_processed' in match_details:
            print(f"  - 处理文本总数: {match_details['total_texts_processed']}")
        if 'texts_passed_all_stages' in match_details:
            print(f"  - 通过所有阶段的文本数: {match_details['texts_passed_all_stages']}")
        if 'column_stats' in match_details:
            print(f"  - 列级统计: 简化显示（包含{len(match_details['column_stats'])}列的统计信息）")

        # 验证扩展后的字段结构（包含展开的统计信息）
        expected_core_fields = {
            keyword_field, company_field, 'match_success', 'matched_texts',
            'match_details', 'filter_reason', 'filtered_columns', 'processed_at'
        }
        expected_stats_fields = {
            'total_texts_processed', 'texts_passed_all_stages',
            'like_passed_count', 'must_passed_count', 'unlike_passed_count',
            'like_failed_count', 'must_failed_count', 'unlike_failed_count'
        }
        expected_all_fields = expected_core_fields | expected_stats_fields

        actual_fields = set(result.keys())
        print(f"  - 字段验证: 期望{len(expected_all_fields)}个字段，实际{len(actual_fields)}个字段")

        missing_core = expected_core_fields - actual_fields
        missing_stats = expected_stats_fields - actual_fields
        extra = actual_fields - expected_all_fields

        if not missing_core and not missing_stats:
            print(f"  - ✅ 字段结构完整（核心字段 + 统计字段）")
        else:
            if missing_core:
                print(f"  - ❌ 缺少核心字段: {missing_core}")
            if missing_stats:
                print(f"  - ❌ 缺少统计字段: {missing_stats}")

        if extra:
            print(f"  - ℹ️ 额外字段: {extra}")

        # 验证统计数据的准确性
        if all(field in result for field in expected_stats_fields):
            total_processed = result['total_texts_processed']
            like_total = result['like_passed_count'] + result['like_failed_count']
            print(f"  - 统计验证: 处理总数={total_processed}, like总数={like_total}")
            if total_processed == like_total:
                print(f"  - ✅ 统计数据一致性正确")
            else:
                print(f"  - ❌ 统计数据不一致")
    else:
        print(f"\n❌ 匹配结果: 失败或被过滤")

    # 测试失败案例
    print(f"\n测试失败案例:")
    input_data_fail = {
        'company_id': 'TEST002',
        'company_name': '传统机械公司',
        'main_product': ['传统设备', '机械产品'],
        'service_intro': ['传统制造'],
        'industry': '制造业',
        'region': '广东省深圳市'
    }

    input_row_fail = pd.Series(input_data_fail)
    input_row_fail.name = 1
    result_fail = matcher._process_single_combination(1, keyword_row, input_row_fail)

    if result_fail:
        print(f"❌ 意外的成功匹配: {result_fail}")
    else:
        print(f"✅ 正确的失败结果: 不符合匹配条件的记录被正确过滤")

    # ========== 第二部分：结果保存逻辑测试 ==========
    print(f"\n【第二部分】结果保存逻辑测试")
    print("-" * 50)

    # 创建包含多个匹配结果的测试数据集
    test_results = []

    # 添加成功匹配的结果
    if result:
        test_results.append(result)

    # 创建更多测试数据
    additional_test_cases = [
        {
            'company_id': 'TEST003',
            'company_name': '新能源科技公司',
            'main_product': ['电动摩托车', '电池技术'],
            'service_intro': ['电动车辆研发'],
            'industry': '制造业',
            'region': '上海市',
            'industry_l1_code': 'C36',
            'industry_type': 'C'
        },
        {
            'company_id': 'TEST004',
            'company_name': '智能汽车公司',
            'main_product': ['智能电动汽车', '自动驾驶'],
            'service_intro': ['汽车智能化'],
            'industry': '制造业',
            'region': '北京市',
            'industry_l1_code': 'C36',
            'industry_type': 'C'
        }
    ]

    # 处理额外的测试案例
    for i, additional_data in enumerate(additional_test_cases, start=2):
        additional_row = pd.Series(additional_data)
        additional_row.name = i
        additional_result = matcher._process_single_combination(i, keyword_row, additional_row)
        if additional_result:
            test_results.append(additional_result)

    print(f"创建测试数据集: 共{len(test_results)}条成功匹配的记录")

    # 测试结果过滤逻辑
    print(f"\n测试结果过滤逻辑:")
    filtered_results = matcher._filter_successful_results(test_results)
    print(f"过滤前: {len(test_results)}条记录")
    print(f"过滤后: {len(filtered_results)}条记录")
    print(f"过滤效果: {'✅ 正常' if len(filtered_results) == len(test_results) else '❌ 异常'}")

    # 测试结果保存
    print(f"\n测试结果保存:")
    try:
        # 调用结果处理器保存结果
        output_file_path = matcher.result_processor.process_and_save_results(
            filtered_results,
            output_path=None  # 使用默认路径
        )

        print(f"✅ 结果保存成功")
        print(f"保存文件路径: {output_file_path}")

        # 验证保存的文件
        if Path(output_file_path).exists():
            file_size = Path(output_file_path).stat().st_size
            print(f"文件大小: {file_size} 字节")

            # 读取并验证保存的结果
            try:
                saved_df = pd.read_csv(output_file_path)
                print(f"保存的记录数: {len(saved_df)}")
                print(f"保存的字段: {list(saved_df.columns)}")

                # 验证配置化字段名称
                keyword_field = matcher.keyword_identifier_field
                company_field = matcher.company_identifier_field

                if keyword_field in saved_df.columns:
                    print(f"✅ 配置化关键词字段 '{keyword_field}' 存在")
                else:
                    print(f"❌ 配置化关键词字段 '{keyword_field}' 缺失")

                if company_field in saved_df.columns:
                    print(f"✅ 配置化企业字段 '{company_field}' 存在")
                else:
                    print(f"❌ 配置化企业字段 '{company_field}' 缺失")

                # 验证结果处理器的输出格式
                print(f"\n结果处理器输出格式验证:")

                # 检查匹配文本列（结果处理器将matched_texts展开为多个列）
                matched_text_columns = [col for col in saved_df.columns if col.endswith('_matched_texts')]
                if matched_text_columns:
                    print(f"✅ 匹配文本列已展开: 共{len(matched_text_columns)}列")
                    print(f"   示例列: {matched_text_columns[:3]}...")
                else:
                    print(f"❌ 匹配文本列缺失")

                # 验证必要的标识字段
                required_fields = {keyword_field, company_field}
                missing_required = required_fields - set(saved_df.columns)
                if not missing_required:
                    print(f"✅ 必要标识字段完整: {required_fields}")
                else:
                    print(f"❌ 缺少必要字段: {missing_required}")

                # 验证统计信息字段
                expected_stats_fields = {
                    'total_texts_processed', 'texts_passed_all_stages',
                    'like_passed_count', 'must_passed_count', 'unlike_passed_count',
                    'like_failed_count', 'must_failed_count', 'unlike_failed_count'
                }
                stats_fields_in_csv = expected_stats_fields & set(saved_df.columns)
                missing_stats = expected_stats_fields - set(saved_df.columns)

                if not missing_stats:
                    print(f"✅ 统计信息字段完整: 共{len(stats_fields_in_csv)}个字段")
                    print(f"   统计字段: {sorted(stats_fields_in_csv)}")
                else:
                    print(f"❌ 缺少统计字段: {missing_stats}")
                    print(f"✅ 已有统计字段: {sorted(stats_fields_in_csv)}")

                # 验证数据完整性
                print(f"\n数据完整性验证:")
                non_empty_records = 0
                for _, row in saved_df.iterrows():
                    # 检查是否有非空的匹配文本
                    has_matches = any(row.get(col, '') for col in matched_text_columns)
                    if has_matches:
                        non_empty_records += 1

                print(f"有效匹配记录数: {non_empty_records}/{len(saved_df)}")
                if non_empty_records == len(saved_df):
                    print(f"✅ 所有保存的记录都包含匹配文本")
                else:
                    print(f"❌ 部分记录缺少匹配文本")

                # 显示示例记录
                print(f"\n示例记录（前2条）:")
                for i, (_, row) in enumerate(saved_df.head(2).iterrows()):
                    print(f"  记录{i+1}: {keyword_field}={row.get(keyword_field)}, {company_field}={row.get(company_field)}")

                    # 显示有内容的匹配文本列
                    non_empty_matches = []
                    for col in matched_text_columns:
                        value = row.get(col, '')
                        # 排除空值、NaN、空列表和空字符串
                        if value and str(value) not in ['', '[]', 'nan', 'NaN']:
                            non_empty_matches.append(f"{col}={value}")

                    if non_empty_matches:
                        print(f"           匹配内容: {non_empty_matches[0]}")  # 只显示第一个非空匹配
                        if len(non_empty_matches) > 1:
                            print(f"           (还有{len(non_empty_matches)-1}个其他匹配列)")
                    else:
                        print(f"           匹配内容: 无有效匹配")

                    # 显示统计信息
                    if stats_fields_in_csv:
                        stats_info = []
                        for field in ['total_texts_processed', 'texts_passed_all_stages', 'like_passed_count', 'must_passed_count']:
                            if field in row:
                                stats_info.append(f"{field}={row.get(field, 0)}")
                        if stats_info:
                            print(f"           统计信息: {', '.join(stats_info)}")

                # 验证原始结果数据的完整性（在内存中）
                print(f"\n原始结果数据验证:")
                if filtered_results:
                    sample_result = filtered_results[0]
                    original_fields = set(sample_result.keys())

                    # 核心字段
                    expected_core_fields = {
                        keyword_field, company_field, 'match_success', 'matched_texts',
                        'match_details', 'filter_reason', 'filtered_columns', 'processed_at'
                    }

                    # 展开的统计字段
                    expected_stats_fields = {
                        'total_texts_processed', 'texts_passed_all_stages',
                        'like_passed_count', 'must_passed_count', 'unlike_passed_count',
                        'like_failed_count', 'must_failed_count', 'unlike_failed_count'
                    }

                    expected_all_fields = expected_core_fields | expected_stats_fields

                    missing_core = expected_core_fields - original_fields
                    missing_stats = expected_stats_fields - original_fields

                    if not missing_core and not missing_stats:
                        print(f"✅ 原始结果数据结构完整（{len(expected_core_fields)}个核心字段 + {len(expected_stats_fields)}个统计字段）")
                        print(f"   总字段数: {len(original_fields)}")

                        # 验证match_details的存在
                        if 'match_details' in sample_result and sample_result['match_details']:
                            match_details = sample_result['match_details']
                            print(f"✅ match_details信息完整: {list(match_details.keys())}")
                        else:
                            print(f"❌ match_details信息缺失")

                        # 验证统计字段的数据一致性
                        total_processed = sample_result.get('total_texts_processed', 0)
                        like_passed = sample_result.get('like_passed_count', 0)
                        like_failed = sample_result.get('like_failed_count', 0)
                        like_total = like_passed + like_failed

                        print(f"✅ 统计数据示例: 总处理={total_processed}, like通过={like_passed}, like失败={like_failed}")
                        if total_processed == like_total:
                            print(f"✅ 统计数据一致性验证通过")
                        else:
                            print(f"❌ 统计数据不一致: {total_processed} != {like_total}")
                    else:
                        if missing_core:
                            print(f"❌ 缺少核心字段: {missing_core}")
                        if missing_stats:
                            print(f"❌ 缺少统计字段: {missing_stats}")
                else:
                    print(f"❌ 无原始结果数据可验证")

            except Exception as e:
                print(f"❌ 读取保存文件失败: {e}")
        else:
            print(f"❌ 保存文件不存在: {output_file_path}")

    except Exception as e:
        print(f"❌ 结果保存失败: {e}")

    print(f"\n{'='*80}")
    print(f"完整数据处理流程测试完成")
    print(f"{'='*80}")


if __name__ == "__main__":
    test_keyword_matcher()
    test_single_combination_processing()
